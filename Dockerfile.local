ARG DEPS_IMAGE=node:22-alpine
ARG RUN_IMAGE=node:22-alpine
###########################

# Stage 1: Dependency installation

FROM $DEPS_IMAGE AS deps

WORKDIR /app

# Copy files required for dependency installation
COPY .npmrc .npmrc
COPY package.json package-lock.json* ./

# Installs prod dependencies
RUN npm install --legacy-peer-deps
# Installs dev dependencies & removes the credentials in the same layer
RUN npm install --only=development && rm -r .npmrc
COPY prisma ./prisma
RUN npm run generate
###########################
###########################

### Stage 2: Runtime

FROM $RUN_IMAGE AS runtime

WORKDIR /app

COPY . .
COPY --from=deps /app/package.json /app/package-lock.json* ./
COPY --from=deps /app/node_modules /node_modules

# App's port
EXPOSE 4000
# Start the server
CMD ["npm", "run", "dev"]
# CMD ["sh", "-c", "npm run migrate && npm run seed && npm run dev"]

###########################
