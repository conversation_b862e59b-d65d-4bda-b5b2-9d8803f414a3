import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { getScoreField } from '@consts/reward/profile';
import { RewardActionInputZ } from '@schemas/reward/profile';

export const RewardActionModule = {
  assignReward: async ({ profileId, rewardId }: RewardActionInputZ) => {
    const reward = await prismaPG.reward.findUnique({ where: { id: rewardId } });
    if (!reward) throw new AppError('RWD001');

    const field = getScoreField(reward.type);
    const result = await prismaPG.$transaction(async (tx) => {
      const user = await tx.profile.findUnique({ where: { id: profileId } });
      if (!user) throw new AppError('PFL001');
      const profile = await tx.rewardProfile.findUnique({ where: { profileId } });

      const updatedRewardProfile = profile
        ? await tx.rewardProfile.update({
            where: { profileId },
            data: {
              [field]: { increment: reward.score },
              totalScore: { increment: reward.score },
            },
          })
        : await tx.rewardProfile.create({
            data: {
              profileId,
              [field]: reward.score,
              totalScore: reward.score,
            },
          });
      const assigned = await tx.rewardAssigned.create({
        data: {
          profileId,
          rewardId,
          score: reward.score,
        },
      });

      return {
        profile: updatedRewardProfile,
        assigned,
      };
    });

    return result;
  },

  unassignReward: async ({ profileId, rewardId }: RewardActionInputZ) => {
    const reward = await prismaPG.reward.findUnique({ where: { id: rewardId } });
    if (!reward) throw new AppError('RWD001');

    const field = getScoreField(reward.type);
    const result = await prismaPG.$transaction(async (tx) => {
      const profile = await tx.rewardProfile.findUnique({
        where: { profileId },
        select: {
          [field]: true,
          totalScore: true,
        },
      });

      if (!profile) {
        throw new AppError('RWD002');
      }

      const current = profile[field];
      const total = profile.totalScore;

      if (!(current - reward.score >= 0 && total - reward.score >= 0)) {
        throw new AppError('RWD003');
      }

      await tx.rewardAssigned.deleteMany({ where: { profileId, rewardId } });

      const updatedProfile = await tx.rewardProfile.update({
        where: { profileId },
        data: {
          [field]: { decrement: reward.score },
          totalScore: { decrement: reward.score },
        },
      });
      return updatedProfile;
    });

    return result;
  },
};
