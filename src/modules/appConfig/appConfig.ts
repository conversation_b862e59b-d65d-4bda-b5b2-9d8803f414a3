import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import {
  AnnouncementNearByConfigI,
  AuthSessionAppConfigI,
  CommunicationConfigI,
  ForumQuestionConfigI,
} from '@interfaces/appConfig/appConfig';
import { ModuleE, Prisma, SubModuleE } from '@prisma/mongodb';
const AppConfigModule = {
  fetchById: async (
    filters: Prisma.AppConfigWhereInput,
    select: Prisma.AppConfigSelect = {
      id: true,
      config: true,
    },
  ): Promise<AnnouncementNearByConfigI | AuthSessionAppConfigI | CommunicationConfigI | ForumQuestionConfigI> => {
    const appConfigResult = await prismaMG.appConfig.findFirst({
      where: filters,
      select,
    });
    if (!appConfigResult) {
      throw new AppError('APPCFG001');
    }
    switch (filters.module) {
      case ModuleE.ANNOUNCEMENT: {
        switch (filters.subModule) {
          case SubModuleE.NEAR_BY: {
            return appConfigResult.config as AnnouncementNearByConfigI;
          }
        }
        break;
      }
      case ModuleE.AUTH: {
        switch (filters.subModule) {
          case SubModuleE.SESSION: {
            return appConfigResult.config as AuthSessionAppConfigI;
          }
        }
        break;
      }
      case ModuleE.COMMUNICATION: {
        switch (filters.subModule) {
          case SubModuleE.COMMUNICATION: {
            return appConfigResult.config as CommunicationConfigI;
          }
        }
        break;
      }
      case ModuleE.FORUM: {
        switch (filters.subModule) {
          case SubModuleE.QUESTION: {
            return appConfigResult.config as ForumQuestionConfigI;
          }
        }
        break;
      }
    }
    throw new AppError('APPCFG001');
  },
};
export default AppConfigModule;
