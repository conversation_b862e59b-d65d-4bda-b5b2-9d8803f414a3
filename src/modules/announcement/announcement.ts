import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import Master from '@modules/master';
import { RawDataModule } from '@modules/rawData';
import { Prisma } from '@prisma/postgres';
import type { Announcement } from '@prisma/postgres';
import type { AnnouncementCreateOneI } from '@schemas/announcement/announcement';
import { isFilled } from '@utils/data/object';

export const CoreAnnouncementModule = {
  createOne: async ({
    title,
    description,
    latitude,
    longitude,
    addressMapBox,
    city,
    cityMapbox,
    countryIso2,
    startDate,
    endDate,
    startTime,
    endTime,
  }: AnnouncementCreateOneI): Promise<Partial<Announcement>> => {
    const cityFilter: Prisma.CityRawDataWhereInput = {};

    let cityInput: Prisma.CityRawDataUncheckedCreateInput;
    if (isFilled(city)) {
      cityFilter.id = city.id;
    } else if (isFilled(cityMapbox)) {
      cityFilter.countryIso2 = countryIso2;
      cityFilter.OR = [
        {
          mapboxId: {
            equals: cityMapbox.id,
            mode: 'insensitive',
          },
        },
        {
          name: {
            contains: cityMapbox.name,
            mode: 'insensitive',
          },
        },
      ];
      cityInput = {
        mapboxId: cityMapbox.id,
        name: cityMapbox.name,
        countryIso2,
        latitude,
        longitude,
      };
    }
    const [_country, cityResult] = await Promise.all([
      Master.CountryModule.fetchOne({ iso2: countryIso2 }),
      Master.CityModule.fetchsert(cityInput, cityFilter, city.dataType),
    ]);
    const addressRawData = await (isFilled(addressMapBox)
      ? RawDataModule.AddressRawDataModule.fetchsert(
          { mapboxId: addressMapBox.id, text: addressMapBox.text, latitude, longitude, countryIso2, city, cityMapbox },
          {
            mapboxId: addressMapBox.id,
          },
        )
      : null);
    const input: Prisma.AnnouncementUncheckedCreateInput = {
      countryIso2,
      title,
      description,
      startDate,
      endDate,
      startTime,
      endTime,
    };
    if (cityResult.dataType === 'master') {
      input.cityId = cityResult.id;
    } else {
      input.cityRawDataId = cityResult.id;
    }
    if (addressRawData) {
      input.addressRawDataId = addressRawData.id;
      input.latitude = latitude;
      input.longitude = longitude;
    } else if (cityResult) {
      input.latitude = cityResult.latitude;
      input.longitude = cityResult.longitude;
    }
    const announcement = await prismaPG.announcement.create({ data: input, select: { id: true, cursorId: true } });
    if (!announcement) {
      throw new AppError('ANC005');
    }
    return announcement;
  },
};
