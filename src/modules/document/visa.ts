import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { FastifyStateI } from '@interfaces/common/declaration';
import {
  VisaCreateOneParamsI,
  VisaUpdateOneDataI,
  VisaUpdateOneI,
  VisaForInternalClientI,
  VisaCreateForClientI,
  VisaForExternalClientI,
  VisaUpdateForClientI,
} from '@interfaces/document/visa';
import Master from '@modules/master';
import StorageModule from '@modules/storage';
import { Prisma, Visa } from '@prisma/postgres';
import { ProfileIdPaginationI } from '@schemas/common/common';

import { getExpiryStatus } from '@utils/data/date';

export const VisaModule = {
  fetchOne: async (
    filters: Partial<Pick<Prisma.VisaWhereInput, 'id' | 'profileId'>>,
    select: Prisma.VisaSelect = {
      id: true,
    },
  ): Promise<Visa> => {
    const visaResult = await prismaPG.visa.findFirst({
      where: filters,
      select,
    });
    if (!visaResult) {
      throw new AppError('VISA001');
    }
    return visaResult;
  },
  fetchOneForClient: async (
    filters: Pick<Prisma.VisaWhereInput, 'id'>,
    select: Prisma.VisaSelect = {
      id: true,
      documentNo: true,
      name: true,
      fromDate: true,
      untilDate: true,
      fileUrl: true,
      Country: {
        select: {
          iso2: true,
          name: true,
        },
      },
    },
  ): Promise<VisaForInternalClientI> => {
    const visaResult = await prismaPG.visa.findFirst({
      where: filters,
      select,
    });
    if (!visaResult) {
      throw new AppError('VISA001');
    }
    const visaForClientResult: VisaForInternalClientI = {
      id: visaResult.id,
      documentNo: visaResult.documentNo,
      name: visaResult.name,
      fromDate: visaResult.fromDate,
      untilDate: visaResult.untilDate,
      fileUrl: visaResult.fileUrl,
      country: visaResult.Country,
      expiryStatus: getExpiryStatus(visaResult),
    };
    return visaForClientResult;
  },
  createOne: async (state: FastifyStateI, params: VisaCreateOneParamsI): Promise<VisaCreateForClientI> => {
    const [visaResult, _profileMetaResult] = await prismaPG.$transaction(
      async (txn) =>
        await Promise.all([
          txn.visa.create({
            data: {
              fileUrl: params.file?.fileUrl ?? null,
              fromDate: params.fromDate,
              untilDate: params.untilDate,
              name: params.name,
              profileId: state.profileId,
              countryIso2: params.countryIso2,
              documentNo: params.documentNo,
            },
            select: {
              id: true,
              name: true,
              documentNo: true,
              fromDate: true,
              untilDate: true,
              fileUrl: true,
              Country: {
                select: {
                  iso2: true,
                  name: true,
                },
              },
            },
          }),
          txn.profileMeta.update({
            select: { visaCount: true },
            data: {
              visaCount: { increment: 1 },
            },
            where: { profileId: state.profileId },
          }),
        ]),
    );

    if (!visaResult) {
      throw new AppError('VISA002');
    }

    const visaCreateEditForClientResult: VisaCreateForClientI = {
      id: visaResult.id!,
      fileUrl: visaResult.fileUrl ?? null,
      expiryStatus: getExpiryStatus(visaResult),
    };

    return visaCreateEditForClientResult;
  },
  updateOne: async (
    params: VisaUpdateOneI,
    filters: Pick<Prisma.VisaWhereUniqueInput, 'id'>,
  ): Promise<VisaUpdateForClientI> => {
    const [existingVisaResult, _countryResult] = await Promise.all([
      VisaModule.fetchOne({ id: filters.id }, { id: true, fromDate: true, untilDate: true, fileUrl: true }),
      params?.countryIso2?.length ? Master.CountryModule.fetchOne({ iso2: params.countryIso2 }) : null,
    ]);

    if (params?.fromDate && params?.untilDate && params?.fromDate > params?.untilDate) {
      throw new AppError('VISA001');
    } else if (params?.fromDate && existingVisaResult.untilDate && params.fromDate > existingVisaResult.untilDate) {
      throw new AppError('VISA001');
    } else if (params?.untilDate && existingVisaResult.fromDate > params.untilDate!) {
      throw new AppError('VISA001');
    }

    if (params?.file?.opr) {
      if (params.file.opr === 'UPDATE' && params?.file?.fileUrl && existingVisaResult.fileUrl) {
        try {
          await StorageModule.CoreStorageModule.deleteFile({ fileUrl: existingVisaResult.fileUrl });
        } catch (_error) {}
      } else if (params.file.opr === 'DELETE' && existingVisaResult.fileUrl) {
        try {
          await StorageModule.CoreStorageModule.deleteFile({ fileUrl: existingVisaResult.fileUrl });
        } catch (_error) {}
      }
    }

    const toUpdateParams: VisaUpdateOneDataI = {};
    if (params?.name) {
      toUpdateParams.name = params.name;
    }
    toUpdateParams.untilDate = params.untilDate;
    if (params?.countryIso2) {
      toUpdateParams.countryIso2 = params.countryIso2;
    }
    if (params?.documentNo) {
      toUpdateParams.documentNo = params.documentNo;
    }
    if (params?.file?.opr) {
      if (params.file.opr === 'CREATE' || params.file.opr === 'UPDATE') {
        if (params?.file?.fileUrl) {
          toUpdateParams.fileUrl = params.file.fileUrl;
        }
      } else if (params.file.opr === 'DELETE') {
        toUpdateParams.fileUrl = null;
      }
    }
    const visaResult = await prismaPG.visa.update({
      data: toUpdateParams,
      where: {
        id: filters.id,
      },
      select: {
        id: true,
        fromDate: true,
        untilDate: true,
        fileUrl: true,
      },
    });
    if (!visaResult) {
      throw new AppError('VISA003');
    }
    const visaForClientResult: VisaUpdateForClientI = {
      id: visaResult.id!,
      fileUrl: visaResult.fileUrl || null,
      expiryStatus: getExpiryStatus(visaResult),
    };
    return visaForClientResult;
  },
  fetchForExternalClient: async (
    { profileId, page, pageSize }: ProfileIdPaginationI,
    orderBy: Prisma.VisaOrderByWithRelationInput = { createdAt: 'desc' },
    select: Prisma.VisaSelect = {
      id: true,
      documentNo: true,
      name: true,
      fromDate: true,
      untilDate: true,
      fileUrl: true,
      Country: {
        select: { name: true },
      },
    },
  ): Promise<VisaForExternalClientI[]> => {
    const visaResult = await prismaPG.visa.findMany({
      where: {
        profileId,
      },
      skip: page * pageSize,
      take: pageSize,
      orderBy,
      select,
    });
    const visaForClientResult: VisaForExternalClientI[] = [];
    if (visaResult?.length) {
      visaForClientResult.push(
        ...visaResult.map(
          (visa) =>
            ({
              id: visa.id,
              name: visa.name,
              fromDate: visa.fromDate,
              untilDate: visa.untilDate,
              fileUrl: visa.fileUrl,
              country: visa.Country,
              expiryStatus: getExpiryStatus(visa),
            }) as VisaForExternalClientI,
        ),
      );
    }
    return visaForClientResult;
  },
  deleteOne: async ({ id }: Pick<Prisma.VisaWhereUniqueInput, 'id'>): Promise<void> => {
    const existingVisaResult = await VisaModule.fetchOne(
      { id },
      { id: true, fromDate: true, untilDate: true, profileId: true },
    );

    if (!existingVisaResult) {
      throw new AppError('VISA001');
    }

    await prismaPG.$transaction(async (txn) => {
      const deleteResult = await txn.visa.delete({
        where: { id },
      });

      if (!deleteResult) {
        throw new AppError('VISA010');
      }

      const visaCount = await txn.visa.count({
        where: { profileId: existingVisaResult.profileId },
      });

      await txn.profileMeta.update({
        where: { profileId: existingVisaResult.profileId },
        data: {
          visaCount: visaCount === 0 ? 0 : { decrement: 1 },
        },
      });
    });

    return;
  },
};
