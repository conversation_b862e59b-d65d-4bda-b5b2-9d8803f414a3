import { prismaPG } from '@config/db';
import AppError from '@classes/AppError';
import { Prisma } from '@prisma/postgres';
import { AddressRawDataParams } from '@schemas/rawData/addressRawData';
import Master from '@modules/master';
import { isFilled, pick } from '@utils/data/object';
import type { AddressRawDataResult } from '@interfaces/rawData/addressRawData';

export const AddressRawDataModule = {
  fetchsert: async (
    { city, cityMapbox, countryIso2, latitude, longitude, mapboxId, text }: AddressRawDataParams,
    filters: Prisma.AddressRawDataWhereInput,
  ): Promise<AddressRawDataResult> => {
    const select: Prisma.AddressRawDataSelect = {
      id: true,
      latitude: true,
      longitude: true,
      mapboxId: true,
      City: {
        select: {
          id: true,
          name: true,
        },
      },
      CityRawData: {
        select: {
          id: true,
          name: true,
        },
      },
    };
    let addressRawData = await prismaPG.addressRawData.findFirst({
      where: filters,
      select,
    });
    if (addressRawData) {
      return {
        ...pick(addressRawData, ['id', 'latitude', 'longitude', 'mapboxId', 'text']),
        city: Master.CityModule.transform(addressRawData),
      } as AddressRawDataResult;
    }

    const cityInput: Prisma.CityRawDataUncheckedUpdateInput = {};
    const cityFilter: Prisma.CityRawDataWhereInput = {};
    if (isFilled(city)) {
      cityFilter.id = city.id;
    } else if (isFilled(cityMapbox)) {
      cityFilter.countryIso2 = countryIso2;
      cityFilter.OR = [
        {
          mapboxId: {
            equals: cityMapbox.id,
            mode: 'insensitive',
          },
        },
        {
          name: {
            contains: cityMapbox.name,
            mode: 'insensitive',
          },
        },
      ];
      cityInput.name = cityMapbox.name;
      cityInput.latitude = latitude;
      cityInput.longitude = longitude;
    }
    const cityResult = await Master.CityModule.fetchsert(cityInput, cityFilter, city?.dataType);

    const input: Prisma.AddressRawDataUncheckedCreateInput = {
      countryIso2,
      latitude,
      longitude,
      text,
      mapboxId,
    };
    if (cityResult.dataType === 'master') {
      input.cityId = cityResult.id;
    } else {
      input.cityRawDataId = cityResult.id;
    }
    addressRawData = await prismaPG.addressRawData.create({
      data: input,
      select,
    });
    if (!addressRawData) {
      throw new AppError('ADDR002');
    }
    return {
      ...pick(addressRawData, ['id', 'latitude', 'longitude', 'mapboxId', 'text']),
      city: Master.CityModule.transform(addressRawData),
    } as AddressRawDataResult;
  },
};
