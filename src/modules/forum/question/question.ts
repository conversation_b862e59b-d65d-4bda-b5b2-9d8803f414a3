import { prismaPG } from '@config/db';
import type {
  ForumQuestionDetailWithAnswersI,
  ForumQuestionDetailWithAnswersSQLI,
  ForumQuestionFetchManyResultI,
  ForumQuestionFetchManySQLI,
  ForumQuestionListingI,
  FetchForumQuestionListingI,
  ForumQuestionSearchItemI,
  ForumQuestionSearchResultI,
  TroubleshootQuestionI,
} from '@interfaces/forum/question';
import type { GlobalSearchQuestionItemI, GlobalSearchResponseI } from '@interfaces/forum/search';
import type {
  ForumQuestionUpdateLiveI,
  ForumQuestionCreateOneI,
  ForumQuestionDeleteOneI,
  ForumQuestionFetchManyI,
  ForumQuestionSearchI,
  GlobalSearchParamsI,
  ForumQuestionListingInputI,
} from '@schemas/forum/question';
import { MemberTypeE, Prisma, Question } from '@prisma/postgres';
import type { FastifyStateI } from '@interfaces/common/declaration';
import AppError from '@classes/AppError';
import { errorHandler } from '@utils/errors/handler';
import { CommunityModule } from '../community/community';
import CommunityMemberModule from '../member';
import { DepartmentModule } from '@modules/company/department';
import { EquipmentCategoryModule } from '@modules/ship/equipmentCategory';
import { EquipmentModelModule } from '@modules/ship/equipmentModel';
import { EquipmentManufacturerModule } from '@modules/ship/equipmentManufacturer';
import { TopicModule } from '../topic';
import { isNullUndefined } from '@utils/data/data';
import type { DateNullI, NumberNullI, TotalCursorDateDataI } from '@interfaces/common/data';
import Ship from '@modules/ship';
import type { RouteParamsI } from '@schemas/common/common';
import { addMsToDate, getCurrentDate, subMsToDate } from '@utils/data/date';
import AppConfig from '@modules/appConfig';
import type { ForumQuestionConfigI } from '@interfaces/appConfig/appConfig';
import type { TotalI } from '@interfaces/common/db';
import { generateSlug } from '@utils/data/slugger';

export const QuestionModule = {
  fetchById: async (
    { id }: Pick<Prisma.QuestionWhereUniqueInput, 'id'>,
    select: Prisma.QuestionSelect = {
      id: true,
    },
  ) => {
    const questionResult = await prismaPG.question.findUnique({
      select,
      where: {
        id,
      },
    });
    if (!questionResult) {
      throw new AppError('FMQUE008');
    }
    return questionResult;
  },
  fetchBySlug: async (
    { slug }: Pick<Prisma.QuestionWhereUniqueInput, 'slug'>,
    select: Prisma.QuestionSelect = {
      id: true,
    },
  ) => {
    const questionResult = await prismaPG.question.findUnique({
      select,
      where: {
        slug,
      },
    });
    if (!questionResult) {
      throw new AppError('FMQUE008');
    }
    return questionResult;
  },
  createOne: async (
    state: FastifyStateI,
    {
      communityId,
      department,
      description,
      equipmentCategory,
      equipmentManufacturer,
      equipmentModel,
      files,
      isAnonymous,
      isLive,
      ship,
      title,
      topics,
      type,
    }: ForumQuestionCreateOneI,
  ): Promise<Pick<Question, 'id'>> => {
    try {
      const selfProfileId = state.profileId;
      const communityResult = await CommunityModule.fetchById({
        id: communityId,
      });
      if (!communityResult) {
        throw new AppError('CMTY007');
      }
      if (
        !(
          communityResult?.access === 'GLOBAL' ||
          (communityResult?.access === 'PUBLIC' && !communityResult?.isRestricted)
        )
      ) {
        await CommunityMemberModule.fetchMember({
          communityId,
        });
      }
      const questionInput: Prisma.QuestionUncheckedCreateInput = {
        title: title,
        description: description,
        type: type,
        communityId: communityId,
        profileId: selfProfileId,
        isLive,
        slug: generateSlug(title),
      };
      if (isLive) {
        questionInput.liveStartedAt = getCurrentDate();
      }
      const [_departmentResult, shipResult] = await Promise.all([
        department ? DepartmentModule.fetchById(department, { id: true }) : null,
        ship
          ? Ship.CoreShipModule.fetchByImoSelected(ship, null, { subVesselTypeId: true, mainVesselTypeId: true })
          : null,
      ]);
      switch (department?.dataType) {
        case 'master': {
          questionInput.departmentAlternativeId = department.id;
          break;
        }
        case 'raw': {
          questionInput.departmentRawDataId = department.id;
          break;
        }
      }
      if (shipResult) {
        if (shipResult.dataType === 'master') {
          questionInput.shipImo = ship.imo;
          questionInput.mainVesselTypeId = shipResult.mainVesselTypeId;
          questionInput.subVesselTypeId = shipResult.imo;
        } else if (shipResult.dataType === 'raw') {
          questionInput.shipRawDataImo = shipResult.imo;

          if (shipResult.mainVesselTypeId) {
            questionInput.mainVesselTypeId = shipResult.mainVesselTypeId;
          } else if (shipResult.mainVesselTypeRawDataId) {
            questionInput.mainVesselTypeRawDataId = shipResult.mainVesselTypeRawDataId;
          }

          if (shipResult.subVesselTypeId) {
            questionInput.subVesselTypeId = shipResult.subVesselTypeId;
          } else if (shipResult.subVesselTypeRawDataId) {
            questionInput.subVesselTypeRawDataId = shipResult.subVesselTypeRawDataId;
          }
        }
      }

      if (!isNullUndefined(isAnonymous)) {
        questionInput.isAnonymous = isAnonymous;
      }
      if (files?.length) {
        questionInput.QuestionMedia = {
          createMany: {
            data: files.map((file) => ({ communityId, fileUrl: file.fileUrl, fileExtension: file.fileExtension })),
          },
        };
      }

      if (type === 'NORMAL') {
        await Promise.all(topics.map((topic) => TopicModule.fetchById(topic)));
        questionInput.QuestionTopic = {
          createMany: {
            data: topics.map((topic) =>
              topic.dataType === 'master'
                ? {
                    topicId: topic.id,
                    communityId: communityId,
                  }
                : {
                    topicRawDataId: topic.id,
                    communityId: communityId,
                  },
            ),
          },
        };
      } else {
        await Promise.all([
          EquipmentCategoryModule.fetchById(equipmentCategory),
          EquipmentManufacturerModule.fetchById(equipmentManufacturer),
          EquipmentModelModule.fetchById(equipmentModel),
        ]);
        if (equipmentCategory.dataType === 'master') {
          questionInput.equipmentCategoryId = equipmentCategory.id;
        } else {
          questionInput.equipmentCategoryRawDataId = equipmentCategory.id;
        }
        if (equipmentManufacturer.dataType === 'master') {
          questionInput.equipmentManufacturerId = equipmentManufacturer.id;
        } else {
          questionInput.equipmentManufacturerRawDataId = equipmentManufacturer.id;
        }
        if (equipmentModel.dataType === 'master') {
          questionInput.equipmentModelId = equipmentModel.id;
        } else {
          questionInput.equipmentModelRawDataId = equipmentModel.id;
        }
      }
      const [questionResult, _updatedCommunity] = await prismaPG.$transaction(
        async (txn) =>
          await Promise.all([
            txn.question.create({
              data: questionInput,
              select: {
                id: true,
                title: true,
                description: true,
                type: true,
                slug: true,
              },
            }),
            txn.community.update({
              data: { questionCount: { increment: 1 } },
              where: {
                id: communityId,
              },
            }),
          ]),
      );
      if (!questionResult) {
        throw new AppError('FMQUE003');
      }
      return { id: questionResult.id };
    } catch (error) {
      errorHandler(error);
    }
  },
  deleteOne: async (state: FastifyStateI, { id: questionId }: ForumQuestionDeleteOneI) => {
    const selfProfileId = state.profileId;

    const question = await prismaPG.question.findUnique({
      where: {
        id: questionId,
      },
      select: {
        profileId: true,
        communityId: true,
      },
    });

    if (!question) {
      throw new AppError('DB002');
    }

    const communityId = question.communityId;
    const isAuthor = question.profileId === selfProfileId;
    if (!isAuthor) {
      const communityMember = await prismaPG.communityMember.findUnique({
        where: {
          communityId_profileId: {
            communityId,
            profileId: selfProfileId,
          },
        },
        select: {
          type: true,
        },
      });
      if (
        !communityMember ||
        (communityMember.type !== MemberTypeE.ADMIN && communityMember.type !== MemberTypeE.MODERATOR)
      ) {
        throw new AppError('FMQUE006');
      }
    }

    return await prismaPG.$transaction(async (tx) => {
      const questionTopics = await tx.questionTopic.findMany({
        where: { questionId },
        select: {
          topicId: true,
          topicRawDataId: true,
        },
      });
      await tx.answer.deleteMany({ where: { questionId } });
      await tx.answerVote.deleteMany({
        where: {
          Answer: {
            questionId,
          },
        },
      });
      await tx.questionVote.deleteMany({ where: { questionId } });
      await tx.questionMedia.deleteMany({ where: { questionId } });
      await tx.questionTopic.deleteMany({ where: { questionId } });
      const deletedQuestion = await tx.question.delete({ where: { id: questionId } });

      await Promise.all([
        ...questionTopics
          .filter((qt) => qt.topicId)
          .map((qt) =>
            tx.topic.update({
              where: { id: qt.topicId! },
              data: { count: { decrement: 1 } },
            }),
          ),

        ...questionTopics
          .filter((qt) => qt.topicRawDataId)
          .map((qt) =>
            tx.topicRawData.update({
              where: { id: qt.topicRawDataId! },
              data: { count: { decrement: 1 } },
            }),
          ),
      ]);

      return deletedQuestion;
    });
  },
  fetchOneForClient: async (
    state: FastifyStateI,
    { id: questionId }: RouteParamsI,
  ): Promise<ForumQuestionDetailWithAnswersI> => {
    const selfProfileId = state.profileId;
    const questionResult = await QuestionModule.fetchById({ id: questionId }, { communityId: true, isAnonymous: true });
    const communityId = questionResult.communityId;
    await CommunityModule.isPermitted({ communityId, profileId: selfProfileId });

    const questionTempArr: ForumQuestionDetailWithAnswersSQLI[] = await prismaPG.$queryRaw`
  WITH question_data AS (
    SELECT
      q."id",
      q."title",
      q."description",
      q."type",
      q."slug",
      q."upvoteCount",
      q."downvoteCount",
      q."answerCount",
      q."commentCount",
      q."isLive",
      q."liveStartedAt",
      q."isSolved",
      q."isEdited",
      (
        SELECT v."type" FROM "forum"."QuestionVote" v
        WHERE v."questionId" = q."id"
        AND v."profileId" = ${selfProfileId}::uuid
        LIMIT 1
      ) AS "vote",
      (
        SELECT json_agg(
          json_build_object(
            'id', m."id",
            'fileUrl', m."fileUrl",
            'fileExtension', m."fileExtension"
          )
        )
        FROM "forum"."QuestionMedia" m
        WHERE m."questionId" = q."id"
      ) AS "media",
      q."equipmentCategoryId",
      ec."name" AS "equipmentCategoryName",
      q."equipmentCategoryRawDataId",
      ecr."name" AS "equipmentCategoryRawDataName",
      q."equipmentModelId",
      em."name" AS "equipmentModelName",
      q."equipmentModelRawDataId",
      emr."name" AS "equipmentModelRawDataName",
      q."equipmentManufacturerId",
      emf."name" AS "equipmentManufacturerName",
      q."equipmentManufacturerRawDataId",
      emfr."name" AS "equipmentManufacturerRawDataName",
      q."createdAt",
      p."id" AS "profileId"
      ${
        !questionResult.isAnonymous
          ? Prisma.sql`, p."name" AS "profileName", p."avatar" AS "profileAvatar"`
          : Prisma.empty
      },
      (
        SELECT json_agg(
          CASE
            WHEN qt."topicId" IS NOT NULL THEN
              json_build_object(
                'id', t.id,
                'name', t.name,
                'dataType', 'master'
              )
            ELSE
              json_build_object(
                'id', tr.id,
                'name', tr.name,
                'dataType', 'raw'
              )
          END
        )
        FROM "forum"."QuestionTopic" qt
        LEFT JOIN "forum"."Topic" t ON qt."topicId" = t.id
        LEFT JOIN "rawData"."TopicRawData" tr ON qt."topicRawDataId" = tr.id
        WHERE qt."questionId" = q.id
      ) AS topics
    FROM "forum"."Question" q
    INNER JOIN "user"."Profile" p
      ON q."profileId" = p."id"
      AND p."status" = 'ACTIVE'
    LEFT JOIN "network"."BlockedProfile" b1
      ON b1."blockerId" = ${selfProfileId}::uuid
      AND b1."blockedId" = p."id"
    LEFT JOIN "network"."BlockedProfile" b2
      ON b2."blockerId" = p."id"
      AND b2."blockedId" = ${selfProfileId}::uuid
    LEFT JOIN "ship"."EquipmentCategory" ec
      ON ec."id" = q."equipmentCategoryId"
    LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr
      ON ecr."id" = q."equipmentCategoryRawDataId"
    LEFT JOIN "ship"."EquipmentModel" em
      ON em."id" = q."equipmentModelId"
    LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr."id" = q."equipmentModelRawDataId"
    LEFT JOIN "ship"."EquipmentManufacturer" emf ON emf."id" = q."equipmentManufacturerId"
    LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr."id" = q."equipmentManufacturerRawDataId"
    WHERE q.id = ${questionId}::uuid
      AND b1."blockerId" IS NULL
      AND b2."blockerId" IS NULL
  )
  SELECT
    qd.*,
    (
      SELECT json_agg(
        json_build_object(
          'id', a.id,
          'text', a.text,
          'upvoteCount', a."upvoteCount",
          'downvoteCount', a."downvoteCount",
          'commentCount', a."commentCount",
          'status', a.status,
          'isEdited', a."isEdited",
          'createdAt', a."createdAt",
          'profile', json_build_object(
            'id', ap.id,
            'name', ap.name,
            'avatar', ap."avatar"
          ),
          'media', (
            SELECT json_agg(
              json_build_object(
                'id', m."id",
                'fileUrl', m."fileUrl",
                'fileExtension', m."fileExtension"
              )
            )
            FROM "forum"."AnswerMedia" m
            WHERE m."answerId" = a."id"
          )
        )
      )
      FROM "forum"."Answer" a
      INNER JOIN "user"."Profile" ap
        ON a."profileId" = ap.id
        AND ap."status" = 'ACTIVE'
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = ap."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = ap."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE a."questionId" = qd.id
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
      LIMIT 10
    ) as answers
  FROM question_data qd
`;

    if (!questionTempArr?.length) {
      throw new AppError('FMQUE008');
    }
    const questionTemp = questionTempArr[0];
    const question: ForumQuestionDetailWithAnswersI = {
      id: questionTemp.id,
      title: questionTemp.title,
      description: questionTemp.description,
      slug: questionTemp.slug,
      type: questionTemp.type,
      upvoteCount: questionTemp.upvoteCount,
      downvoteCount: questionTemp.downvoteCount,
      answerCount: questionTemp.answerCount,
      commentCount: questionTemp.commentCount,
      isLive: questionTemp.isLive,
      liveStartedAt: questionTemp.liveStartedAt,
      isSolved: questionTemp.isSolved,
      media: questionTemp?.media,
      vote: questionTemp?.vote,
      equipmentCategory: questionTemp.equipmentCategoryId
        ? {
            id: questionTemp.equipmentCategoryId,
            name: questionTemp.equipmentCategoryName,
            dataType: 'master',
          }
        : questionTemp.equipmentCategoryRawDataId
          ? {
              id: questionTemp.equipmentCategoryRawDataId,
              name: questionTemp.equipmentCategoryRawDataName,
              dataType: 'raw',
            }
          : null,
      equipmentModel: questionTemp.equipmentModelId
        ? {
            id: questionTemp.equipmentModelId,
            name: questionTemp.equipmentModelName,
            dataType: 'master',
          }
        : null,
      equipmentManufacturer: questionTemp.equipmentManufacturerId
        ? {
            id: questionTemp.equipmentManufacturerId,
            name: questionTemp.equipmentManufacturerName,
            dataType: 'master',
          }
        : null,
      createdAt: questionTemp.createdAt,
      canModify: questionTemp.profileId === selfProfileId,
      isEdited: questionTemp.isEdited,
      profile: !questionResult.isAnonymous
        ? { id: questionTemp.profileId, name: questionTemp.profileName, avatar: questionTemp.profileAvatar }
        : null,
      topics: questionTemp.topics,
      answers: questionTemp?.answers || null,
    };
    return question;
  },
  fetchMany: async (
    state: FastifyStateI,
    {
      cursorDate,
      departmentId,
      departmentDataType,
      isLive,
      myAnswered,
      myQuestion,
      myCommunity,
      myRecommended,
      pageSize,
      type,
    }: ForumQuestionFetchManyI,
  ): Promise<TotalCursorDateDataI<ForumQuestionFetchManyResultI>> => {
    pageSize = 100;
    const selfProfileId = state.profileId;
    const currentDate = getCurrentDate();

    const config = (await AppConfig.AppConfigModule.fetchById({
      module: 'FORUM',
      subModule: 'QUESTION',
    })) as ForumQuestionConfigI;
    const Hours24Ago = subMsToDate({ date: currentDate, ms: config.liveExpiry });
    const joins: Prisma.Sql[] = [
      Prisma.sql`
        INNER JOIN "user"."Profile" u
          ON q."profileId" = u."id"
          AND u."status" = 'ACTIVE'
        LEFT JOIN "network"."BlockedProfile" b1
          ON b1."blockerId" = ${selfProfileId}::uuid
          AND b1."blockedId" = u."id"
        LEFT JOIN "network"."BlockedProfile" b2
          ON b2."blockerId" = u."id"
          AND b2."blockedId" = ${selfProfileId}::uuid
      `,
    ];
    const filters: Prisma.Sql[] = [];

    const baseFields = Prisma.sql`
      q."id",
      q."title",
      q."slug",
      q."description",
      q."type",
      q."upvoteCount",
      q."downvoteCount",
      q."answerCount",
      q."commentCount",
      q."profileId",
      q."isSolved",
      q."isEdited",
      q."isAnonymous",
      q."liveStartedAt",
      q."isLive",
      (
        SELECT v."type" FROM "forum"."QuestionVote" v
        WHERE v."questionId" = q."id"
        AND v."profileId" = ${selfProfileId}::uuid
        LIMIT 1
      ) AS "vote",
      (
        SELECT json_agg(
          json_build_object(
            'fileUrl', m."fileUrl",
            'fileExtension', m."fileExtension"
          )
        )
        FROM "forum"."QuestionMedia" m
        WHERE m."questionId" = q."id"
      ) AS "media"
    `;
    const fieldsArr = [baseFields];
    switch (type) {
      case 'ALL': {
        fieldsArr.push(Prisma.sql`
        (
          SELECT json_agg(json_build_object(
            'topicId', t."id",
            'topicName', t."name",
            'topicRawDataId', trw."id",
            'topicRawDataName', trw."name"
          ))
          FROM "forum"."QuestionTopic" qt
          LEFT JOIN "forum"."Topic" t ON t."id" = qt."topicId"
          LEFT JOIN "rawData"."TopicRawData" trw ON trw."id" = qt."topicRawDataId"
          WHERE qt."questionId" = q."id"
        ) AS "topics"
      `);
        joins.push(Prisma.sql`
          LEFT JOIN "ship"."EquipmentCategory" ec ON ec."id" = q."equipmentCategoryId"
          LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr ON ecr."id" = q."equipmentCategoryRawDataId"
          LEFT JOIN "ship"."EquipmentModel" em ON em."id" = q."equipmentModelId"
          LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr."id" = q."equipmentModelRawDataId"
          LEFT JOIN "ship"."EquipmentManufacturer" emf ON emf."id" = q."equipmentManufacturerId"
          LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr."id" = q."equipmentManufacturerRawDataId"
        `);
        fieldsArr.push(Prisma.sql`
          q."equipmentCategoryId",
          ec."name" AS "equipmentCategoryName",
          q."equipmentCategoryRawDataId",
          ecr."name" AS "equipmentCategoryRawDataName",
          q."equipmentModelId",
          em."name" AS "equipmentModelName",
          q."equipmentModelRawDataId",
          emr."name" AS "equipmentModelRawDataName",
          q."equipmentManufacturerId",
          emf."name" AS "equipmentManufacturerName",
          q."equipmentManufacturerRawDataId",
          emfr."name" AS "equipmentManufacturerRawDataName"
        `);
        if (myRecommended) {
          joins.push(Prisma.sql`
          LEFT JOIN "career"."ExperienceShip" xs
          ON (xs."shipImo" = q."shipImo" OR xs."shipRawDataImo" = q."shipRawDataImo")
          AND xs."profileId" = ${selfProfileId}::uuid
        `);
          filters.push(Prisma.sql`xs."id" IS NOT NULL`);
        }
        break;
      }
      case 'NORMAL': {
        fieldsArr.push(Prisma.sql`
        (
          SELECT json_agg(json_build_object(
            'topicId', t."id",
            'topicName', t."name",
            'topicRawDataId', trw."id",
            'topicRawDataName', trw."name"
          ))
          FROM "forum"."QuestionTopic" qt
          LEFT JOIN "forum"."Topic" t ON t."id" = qt."topicId"
          LEFT JOIN "rawData"."TopicRawData" trw ON trw."id" = qt."topicRawDataId"
          WHERE qt."questionId" = q."id"
        ) AS "topics"
      `);
        joins.push(Prisma.sql`
        LEFT JOIN "ship"."EquipmentCategory" ec ON ec."id" = q."equipmentCategoryId"
        LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr ON ecr."id" = q."equipmentCategoryRawDataId"
        LEFT JOIN "ship"."EquipmentModel" em ON em."id" = q."equipmentModelId"
        LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr."id" = q."equipmentModelRawDataId"
        LEFT JOIN "ship"."EquipmentManufacturer" emf ON emf."id" = q."equipmentManufacturerId"
        LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr."id" = q."equipmentManufacturerRawDataId"
      `);
        break;
      }
      case 'TROUBLESHOOT': {
        joins.push(Prisma.sql`
          LEFT JOIN "ship"."EquipmentCategory" ec ON ec."id" = q."equipmentCategoryId"
          LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr ON ecr."id" = q."equipmentCategoryRawDataId"
          LEFT JOIN "ship"."EquipmentModel" em ON em."id" = q."equipmentModelId"
          LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr."id" = q."equipmentModelRawDataId"
          LEFT JOIN "ship"."EquipmentManufacturer" emf ON emf."id" = q."equipmentManufacturerId"
          LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr."id" = q."equipmentManufacturerRawDataId"
        `);
        fieldsArr.push(Prisma.sql`
          q."equipmentCategoryId",
          ec."name" AS "equipmentCategoryName",
          q."equipmentCategoryRawDataId",
          ecr."name" AS "equipmentCategoryRawDataName",
          q."equipmentModelId",
          em."name" AS "equipmentModelName",
          q."equipmentModelRawDataId",
          emr."name" AS "equipmentModelRawDataName",
          q."equipmentManufacturerId",
          emf."name" AS "equipmentManufacturerName",
          q."equipmentManufacturerRawDataId",
          emfr."name" AS "equipmentManufacturerRawDataName"
        `);
        if (myRecommended) {
          joins.push(Prisma.sql`
            LEFT JOIN "career"."ExperienceShip" xs
            ON (xs."shipImo" = q."shipImo" OR xs."shipRawDataImo" = q."shipRawDataImo")
            AND xs."profileId" = ${selfProfileId}::uuid
          `);
          filters.push(Prisma.sql`xs."id" IS NOT NULL`);
        }
        break;
      }
    }

    if (departmentDataType === 'master') {
      joins.push(Prisma.sql`
        LEFT JOIN "company"."DepartmentAlternative" d ON d."id" = q."departmentAlternativeId"
      `);
      filters.push(Prisma.sql`q."departmentAlternativeId" = ${departmentId}::uuid`);
    } else if (departmentDataType === 'raw') {
      joins.push(Prisma.sql`
        LEFT JOIN "rawData"."DepartmentRawData" drw ON drw."id" = q."departmentRawDataId"
      `);
      filters.push(Prisma.sql`q."departmentRawDataId" = ${departmentId}::uuid`);
    }
    if (myAnswered) {
      joins.push(Prisma.sql`
        LEFT JOIN "forum"."Answer" a ON a."questionId" = q."id" AND a."profileId" = ${selfProfileId}::uuid
      `);
      filters.push(Prisma.sql`a."id" IS NOT NULL`);
    }

    if (myCommunity) {
      joins.push(Prisma.sql`
      LEFT JOIN "forum"."CommunityMember" cm ON q."communityId" = cm."communityId" AND cm."profileId" = ${selfProfileId}::uuid
    `);
      joins.push(Prisma.sql`
      LEFT JOIN "forum"."Community" c ON q."communityId" = c."id"
    `);
      filters.push(Prisma.sql`(cm."communityId" IS NOT NULL OR c."access" = 'GLOBAL')`);
    }

    if (cursorDate) {
      filters.push(Prisma.sql`q."liveStartedAt" < ${cursorDate}`);
    }
    if (['NORMAL', 'TROUBLESHOOT'].includes(type)) {
      filters.push(Prisma.sql`q."type" = ${type}::"forum"."QuestionTypeE"`);
    }
    if (isLive) {
      filters.push(Prisma.sql`q."liveStartedAt" >= ${Hours24Ago}`);
    }
    if (myQuestion) {
      filters.push(Prisma.sql`q."profileId" = ${selfProfileId}::uuid`);
    }

    const joinClause = joins.length ? Prisma.join(joins, '\n') : Prisma.empty;
    const whereClause = filters.length
      ? Prisma.sql`
      WHERE ${Prisma.join(filters, ' AND ')}`
      : Prisma.empty;

    // Queries
    const [questionsTemp, totalTemp] = await Promise.all([
      prismaPG.$queryRaw<ForumQuestionFetchManySQLI[]>`
      SELECT
        ${Prisma.join(fieldsArr, ', ')}
      FROM "forum"."Question" q
      ${joinClause}
      ${whereClause}
      ORDER BY q."liveStartedAt" DESC
      LIMIT ${pageSize}
    `,
      prismaPG.$queryRaw<{ count: number }[]>`
      SELECT COUNT(1) as count
      FROM "forum"."Question" q
      ${joinClause}
      ${whereClause}
    `,
    ]);

    // Format output
    const questions: ForumQuestionFetchManyResultI[] = [];
    let nextCursorDate: DateNullI = null;
    const total = Number(totalTemp?.[0]?.count ?? 0);

    if (questionsTemp?.length) {
      nextCursorDate = questionsTemp[questionsTemp.length - 1].liveStartedAt;
      questions.push(
        ...questionsTemp.map(
          (q) =>
            ({
              id: q.id,
              title: q.title,
              description: q.description,
              slug: q.slug,
              type: q.type,
              canModify: q.profileId === selfProfileId,
              upvoteCount: q.upvoteCount,
              downvoteCount: q.downvoteCount,
              answerCount: q.answerCount,
              commentCount: q.commentCount,
              isSolved: q.isSolved,
              isEdited: q.isEdited,
              isAnonymous: q.isAnonymous,
              liveStartedAt: q.liveStartedAt,
              isLive: Hours24Ago < q?.liveStartedAt,
              vote: q.vote,
              equipmentCategory: q.equipmentCategoryId
                ? { id: q.equipmentCategoryId, name: q.equipmentCategoryName, dataType: 'master' }
                : q.equipmentCategoryRawDataId
                  ? { id: q.equipmentCategoryRawDataId, name: q.equipmentCategoryRawDataName, dataType: 'raw' }
                  : null,

              equipmentModel: q.equipmentModelId
                ? { id: q.equipmentModelId, name: q.equipmentModelName, dataType: 'master' }
                : q.equipmentModelRawDataId
                  ? { id: q.equipmentModelRawDataId, name: q.equipmentModelRawDataName, dataType: 'raw' }
                  : null,

              equipmentManufacturer: q.equipmentManufacturerId
                ? { id: q.equipmentManufacturerId, name: q.equipmentManufacturerName, dataType: 'master' }
                : q.equipmentManufacturerRawDataId
                  ? { id: q.equipmentManufacturerRawDataId, name: q.equipmentManufacturerRawDataName, dataType: 'raw' }
                  : null,

              topics: q?.topics?.map((topic) => TopicModule.transform(topic)) ?? null,
              media: q?.media,
            }) as ForumQuestionFetchManyResultI,
        ),
      );
    }
    return { data: questions, nextCursorDate, total };
  },
  updateLive: async (
    state: FastifyStateI,
    { isLive: requestedLiveStatus, questionId }: ForumQuestionUpdateLiveI,
  ): Promise<Pick<Question, 'isLive' | 'liveStartedAt'>> => {
    const selfProfileId = state.profileId;
    const [question, config] = await Promise.all([
      QuestionModule.fetchById({ id: questionId }, { liveStartedAt: true, isLive: true, profileId: true }),
      AppConfig.AppConfigModule.fetchById(
        { module: 'FORUM', subModule: 'QUESTION' },
        { config: true },
      ) as Promise<ForumQuestionConfigI>,
    ]);

    const currentDate = getCurrentDate();
    const expiryDate = question?.liveStartedAt
      ? addMsToDate({ date: question.liveStartedAt, ms: config.liveExpiry })
      : null;
    if (!isNullUndefined(expiryDate) && !question.isLive && currentDate > expiryDate) {
      const _questionTemp = await prismaPG.question.update({
        data: {
          isLive: false,
        },
        where: { id: questionId },
        select: { liveStartedAt: true },
      });
      question.isLive = false;
    }
    if (question?.profileId !== selfProfileId) {
      throw new AppError('FMQUE006');
    }
    const input: Prisma.QuestionUncheckedUpdateInput = {};
    if (requestedLiveStatus) {
      if (question?.isLive) {
        throw new AppError('FMQUE013');
      }
      input.isLive = true;
      input.liveStartedAt = getCurrentDate();
    } else {
      if (!question?.isLive) {
        throw new AppError('FMQUE014');
      }
      input.isLive = false;
      input.liveStartedAt = null;
    }
    const questionResult = await prismaPG.question.update({
      data: input,
      select: {
        isLive: true,
        liveStartedAt: true,
      },
      where: { id: questionId },
    });
    if (!questionResult) {
      throw new AppError('FMQUE015');
    }
    return questionResult;
  },
  search: async (
    state: FastifyStateI,
    { cursorId, pageSize, search }: ForumQuestionSearchI,
  ): Promise<ForumQuestionSearchResultI> => {
    const selfProfileId = state.profileId;
    const [totalResult, questionsResult] = await Promise.all([
      prismaPG.$queryRaw<TotalI[]>`
      SELECT
        COUNT(1)::INTEGER AS total
      FROM "forum"."Question" q
      INNER JOIN "user"."Profile" u
        ON u."id" = q."profileId"
        AND u."status" = 'ACTIVE'
        LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
        LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        q."title" ILIKE ${'%' + search + '%'}
      AND q."cursorId" > ${cursorId}
      AND b1."blockerId" IS NULL
      AND b2."blockerId" IS NULL
      AND q."cursorId" > ${cursorId}
    `,
      prismaPG.$queryRaw<ForumQuestionSearchItemI[]>`
      SELECT
        q."id",
        q."title",
        q."slug",
        q."cursorId"
      FROM "forum"."Question" q
      INNER JOIN "user"."Profile" u
        ON u."id" = q."profileId"
        AND u."status" = 'ACTIVE'
        LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = u."id"
        LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = u."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      WHERE
        q."title" ILIKE ${'%' + search + '%'}
        AND q."cursorId" > ${cursorId}
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
      ORDER BY q."title" ASC
      LIMIT ${pageSize}
    `,
    ]);
    let nextCursorId: NumberNullI = null;
    if (questionsResult?.length) {
      nextCursorId = questionsResult[questionsResult.length - 1].cursorId;
    }
    return {
      nextCursorId,
      data: questionsResult,
      total: Number(totalResult?.[0]?.total || 0),
    } as ForumQuestionSearchResultI;
  },
  fetchTroubleshootQuestions: async (
    state: FastifyStateI,
    days: number = 7,
    limit: number = 5,
    equipmentFilter?: {
      categoryId?: string;
      manufacturerId?: string;
      modelId?: string;
    },
  ): Promise<TroubleshootQuestionI[]> => {
    const dateThreshold = new Date();
    dateThreshold.setDate(dateThreshold.getDate() - days);
    return prismaPG.$queryRaw<TroubleshootQuestionI[]>`
  SELECT
    q.id,
    q.title,
    q.description,
     q.slug,
    q."createdAt" AS "createdAt",
    q."upvoteCount" AS "upvoteCount",
    q."answerCount" AS "answerCount",
    q."isSolved" AS "isSolved",
    json_build_object(
      'id', c.id,
      'name', c.name
    ) AS community,
    json_build_object(
      'category', CASE
        WHEN q."equipmentCategoryId" IS NOT NULL THEN
          json_build_object(
            'id', q."equipmentCategoryId",
            'name', ec.name,
            'dataType', 'master'
          )
        WHEN q."equipmentCategoryRawDataId" IS NOT NULL THEN
          json_build_object(
            'id', q."equipmentCategoryRawDataId",
            'name', ecr.name,
            'dataType', 'raw'
          )
        ELSE NULL
      END,
      'manufacturer', CASE
        WHEN q."equipmentManufacturerId" IS NOT NULL THEN
          json_build_object(
            'id', q."equipmentManufacturerId",
            'name', emf.name,
            'dataType', 'master'
          )
        WHEN q."equipmentManufacturerRawDataId" IS NOT NULL THEN
          json_build_object(
            'id', q."equipmentManufacturerRawDataId",
            'name', emfr.name,
            'dataType', 'raw'
          )
        ELSE NULL
      END,
      'model', CASE
        WHEN q."equipmentModelId" IS NOT NULL THEN
          json_build_object(
            'id', q."equipmentModelId",
            'name', em.name,
            'dataType', 'master'
          )
        WHEN q."equipmentModelRawDataId" IS NOT NULL THEN
          json_build_object(
            'id', q."equipmentModelRawDataId",
            'name', emr.name,
            'dataType', 'raw'
          )
        ELSE NULL
      END
    ) AS equipment
  FROM forum."Question" q
  JOIN forum."Community" c ON q."communityId" = c.id
  LEFT JOIN ship."EquipmentCategory" ec ON ec.id = q."equipmentCategoryId"
  LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr ON ecr.id = q."equipmentCategoryRawDataId"
  LEFT JOIN ship."EquipmentManufacturer" emf ON emf.id = q."equipmentManufacturerId"
  LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr.id = q."equipmentManufacturerRawDataId"
  LEFT JOIN ship."EquipmentModel" em ON em.id = q."equipmentModelId"
  LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr.id = q."equipmentModelRawDataId"
  WHERE q.type = 'TROUBLESHOOT'
  AND q."createdAt" >= ${dateThreshold}
  AND (
    c.access != 'PRIVATE' OR EXISTS (
    SELECT 1 FROM forum."CommunityMember" cm
    WHERE cm."communityId" = c.id AND cm."profileId" = ${state.profileId}
    )
  )
  ${
    equipmentFilter?.categoryId
      ? Prisma.sql`AND (q."equipmentCategoryId" = ${equipmentFilter.categoryId} OR q."equipmentCategoryRawDataId" = ${equipmentFilter.categoryId})`
      : Prisma.empty
  }
  ${
    equipmentFilter?.manufacturerId
      ? Prisma.sql`AND (q."equipmentManufacturerId" = ${equipmentFilter.manufacturerId} OR q."equipmentManufacturerRawDataId" = ${equipmentFilter.manufacturerId})`
      : Prisma.empty
  }
  ${
    equipmentFilter?.modelId
      ? Prisma.sql`AND (q."equipmentModelId" = ${equipmentFilter.modelId} OR q."equipmentModelRawDataId" = ${equipmentFilter.modelId})`
      : Prisma.empty
  }
  ORDER BY
    q."upvoteCount" DESC,
    q."answerCount" DESC,
    q."createdAt" DESC
  LIMIT ${limit}
`;
  },

  globalSearch: async (
    state: FastifyStateI,
    { search, page, pageSize }: GlobalSearchParamsI,
  ): Promise<GlobalSearchResponseI<GlobalSearchQuestionItemI>> => {
    const selfProfileId = state.profileId;
    const offset = page * pageSize;
    const searchTerm = `%${search.toLowerCase()}%`;

    // const tsSearchTerm = search.replace(/[!&|():*]/g, ' ').trim().split(/\s+/).join(' & ');

    try {
      const [totalResult, questionsResult] = await Promise.all([
        prismaPG.$queryRaw<[{ count: bigint }]>`
          SELECT COUNT(DISTINCT q."id") as count
          FROM "forum"."Question" q
          INNER JOIN "forum"."Community" c ON q."communityId" = c."id"
          INNER JOIN "user"."Profile" u ON q."profileId" = u."id" AND u."status" = 'ACTIVE'
          LEFT JOIN "forum"."CommunityMember" cm ON c."id" = cm."communityId" AND cm."profileId" = ${selfProfileId}::uuid
          LEFT JOIN "network"."BlockedProfile" b1 ON b1."blockerId" = ${selfProfileId}::uuid AND b1."blockedId" = u."id"
          LEFT JOIN "network"."BlockedProfile" b2 ON b2."blockerId" = u."id" AND b2."blockedId" = ${selfProfileId}::uuid
          LEFT JOIN "company"."DepartmentAlternative" da ON da."id" = q."departmentAlternativeId"
          LEFT JOIN "rawData"."DepartmentRawData" dr ON dr."id" = q."departmentRawDataId"
          LEFT JOIN "ship"."EquipmentCategory" ec ON ec."id" = q."equipmentCategoryId"
          LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr ON ecr."id" = q."equipmentCategoryRawDataId"
          LEFT JOIN "ship"."EquipmentModel" em ON em."id" = q."equipmentModelId"
          LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr."id" = q."equipmentModelRawDataId"
          LEFT JOIN "ship"."EquipmentManufacturer" emf ON emf."id" = q."equipmentManufacturerId"
          LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr."id" = q."equipmentManufacturerRawDataId"
          LEFT JOIN "forum"."QuestionTopic" qt ON qt."questionId" = q."id"
          LEFT JOIN "forum"."Topic" t ON t."id" = qt."topicId"
          LEFT JOIN "rawData"."TopicRawData" tr ON tr."id" = qt."topicRawDataId"

          WHERE (
            (c."access" = 'PUBLIC' OR c."access" = 'GLOBAL')
            OR (c."access" = 'PRIVATE' AND cm."profileId" IS NOT NULL)
          )
          AND b1."blockerId" IS NULL
          AND b2."blockerId" IS NULL
          AND (
            q."title" ILIKE ${searchTerm}
            OR q."description" ILIKE ${searchTerm}
            OR da."name" ILIKE ${searchTerm}
            OR dr."name" ILIKE ${searchTerm}
            OR ec."name" ILIKE ${searchTerm}
            OR ecr."name" ILIKE ${searchTerm}
            OR em."name" ILIKE ${searchTerm}
            OR emr."name" ILIKE ${searchTerm}
            OR emf."name" ILIKE ${searchTerm}
            OR emfr."name" ILIKE ${searchTerm}
            OR t."name" ILIKE ${searchTerm}
            OR tr."name" ILIKE ${searchTerm}
          )
        `,
        prismaPG.$queryRaw<GlobalSearchQuestionItemI[]>`
          WITH ranked_questions AS (
            SELECT DISTINCT q."id",
            CASE
              WHEN q."title" ILIKE ${searchTerm} THEN 1
              WHEN q."description" ILIKE ${searchTerm} THEN 2
              WHEN da."name" ILIKE ${searchTerm} OR dr."name" ILIKE ${searchTerm} THEN 3
              WHEN ec."name" ILIKE ${searchTerm} OR ecr."name" ILIKE ${searchTerm} THEN 4
              WHEN em."name" ILIKE ${searchTerm} OR emr."name" ILIKE ${searchTerm} THEN 5
              WHEN emf."name" ILIKE ${searchTerm} OR emfr."name" ILIKE ${searchTerm} THEN 6
              WHEN t."name" ILIKE ${searchTerm} OR tr."name" ILIKE ${searchTerm} THEN 7
              ELSE 8
            END AS relevance_score,
            q."createdAt"
            FROM "forum"."Question" q
            INNER JOIN "forum"."Community" c ON q."communityId" = c."id"
            INNER JOIN "user"."Profile" u ON q."profileId" = u."id" AND u."status" = 'ACTIVE'
            LEFT JOIN "forum"."CommunityMember" cm ON c."id" = cm."communityId" AND cm."profileId" = ${selfProfileId}::uuid
            LEFT JOIN "network"."BlockedProfile" b1 ON b1."blockerId" = ${selfProfileId}::uuid AND b1."blockedId" = u."id"
            LEFT JOIN "network"."BlockedProfile" b2 ON b2."blockerId" = u."id" AND b2."blockedId" = ${selfProfileId}::uuid
            LEFT JOIN "company"."DepartmentAlternative" da ON da."id" = q."departmentAlternativeId"
            LEFT JOIN "rawData"."DepartmentRawData" dr ON dr."id" = q."departmentRawDataId"
            LEFT JOIN "ship"."EquipmentCategory" ec ON ec."id" = q."equipmentCategoryId"
            LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr ON ecr."id" = q."equipmentCategoryRawDataId"
            LEFT JOIN "ship"."EquipmentModel" em ON em."id" = q."equipmentModelId"
            LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr."id" = q."equipmentModelRawDataId"
            LEFT JOIN "ship"."EquipmentManufacturer" emf ON emf."id" = q."equipmentManufacturerId"
            LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr."id" = q."equipmentManufacturerRawDataId"
            LEFT JOIN "forum"."QuestionTopic" qt ON qt."questionId" = q."id"
            LEFT JOIN "forum"."Topic" t ON t."id" = qt."topicId"
            LEFT JOIN "rawData"."TopicRawData" tr ON tr."id" = qt."topicRawDataId"

            WHERE (
              (c."access" = 'PUBLIC' OR c."access" = 'GLOBAL')
              OR (c."access" = 'PRIVATE' AND cm."profileId" IS NOT NULL)
            )
            AND b1."blockerId" IS NULL
            AND b2."blockerId" IS NULL
            AND (
              q."title" ILIKE ${searchTerm}
              OR q."description" ILIKE ${searchTerm}
              OR da."name" ILIKE ${searchTerm}
              OR dr."name" ILIKE ${searchTerm}
              OR ec."name" ILIKE ${searchTerm}
              OR ecr."name" ILIKE ${searchTerm}
              OR em."name" ILIKE ${searchTerm}
              OR emr."name" ILIKE ${searchTerm}
              OR emf."name" ILIKE ${searchTerm}
              OR emfr."name" ILIKE ${searchTerm}
              OR t."name" ILIKE ${searchTerm}
              OR tr."name" ILIKE ${searchTerm}
            )
          )
          SELECT
            q."id",
            q."title",
            q."description",
            q."slug",
            q."type",
            q."communityId",
            c."name" as "communityName",
            COALESCE(q."departmentAlternativeId", q."departmentRawDataId") as "departmentAlternativeId",
            COALESCE(da."name", dr."name") as "departmentName",
            COALESCE(q."equipmentCategoryId", q."equipmentCategoryRawDataId") as "equipmentCategoryId",
            COALESCE(ec."name", ecr."name") as "equipmentCategoryName",
            COALESCE(q."equipmentModelId", q."equipmentModelRawDataId") as "equipmentModelId",
            COALESCE(em."name", emr."name") as "equipmentModelName",
            COALESCE(q."equipmentManufacturerId", q."equipmentManufacturerRawDataId") as "equipmentManufacturerId",
            COALESCE(emf."name", emfr."name") as "equipmentManufacturerName",
            q."answerCount",
            q."upvoteCount",
            q."createdAt",
            u."name" as "profileName",
            ARRAY[]::text[] as "matchedFields",
            (
              SELECT COALESCE(
                json_agg(
                  CASE
                    WHEN qt."topicId" IS NOT NULL THEN
                      json_build_object(
                        'id', t."id",
                        'name', t."name",
                        'dataType', 'master'
                      )
                    ELSE
                      json_build_object(
                        'id', tr."id",
                        'name', tr."name",
                        'dataType', 'raw'
                      )
                  END
                ) FILTER (WHERE t."id" IS NOT NULL OR tr."id" IS NOT NULL),
                '[]'::json
              )
              FROM "forum"."QuestionTopic" qt
              LEFT JOIN "forum"."Topic" t ON qt."topicId" = t."id"
              LEFT JOIN "rawData"."TopicRawData" tr ON qt."topicRawDataId" = tr."id"
              WHERE qt."questionId" = q."id"
            ) as "topics"
          FROM "forum"."Question" q
          INNER JOIN ranked_questions rq ON q."id" = rq."id"
          INNER JOIN "forum"."Community" c ON q."communityId" = c."id"
          INNER JOIN "user"."Profile" u ON q."profileId" = u."id"
          LEFT JOIN "company"."DepartmentAlternative" da ON da."id" = q."departmentAlternativeId"
          LEFT JOIN "rawData"."DepartmentRawData" dr ON dr."id" = q."departmentRawDataId"
          LEFT JOIN "ship"."EquipmentCategory" ec ON ec."id" = q."equipmentCategoryId"
          LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr ON ecr."id" = q."equipmentCategoryRawDataId"
          LEFT JOIN "ship"."EquipmentModel" em ON em."id" = q."equipmentModelId"
          LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr."id" = q."equipmentModelRawDataId"
          LEFT JOIN "ship"."EquipmentManufacturer" emf ON emf."id" = q."equipmentManufacturerId"
          LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr."id" = q."equipmentManufacturerRawDataId"

          ORDER BY rq.relevance_score ASC, rq."createdAt" DESC
          LIMIT ${pageSize}
          OFFSET ${offset}
        `,
      ]);

      const total = Number(totalResult?.[0]?.count || 0);

      const data = questionsResult.map((item) => {
        const matchedFields: string[] = [];
        const searchLower = search.toLowerCase();

        if (item.title && item.title.toLowerCase().includes(searchLower)) {
          matchedFields.push('title');
        }
        if (item.description && item.description.toLowerCase().includes(searchLower)) {
          matchedFields.push('description');
        }
        if (item.departmentName && item.departmentName.toLowerCase().includes(searchLower)) {
          matchedFields.push('department');
        }
        if (item.equipmentCategoryName && item.equipmentCategoryName.toLowerCase().includes(searchLower)) {
          matchedFields.push('equipmentCategory');
        }
        if (item.equipmentModelName && item.equipmentModelName.toLowerCase().includes(searchLower)) {
          matchedFields.push('equipmentModel');
        }
        if (item.equipmentManufacturerName && item.equipmentManufacturerName.toLowerCase().includes(searchLower)) {
          matchedFields.push('equipmentManufacturer');
        }
        if (item.topics && Array.isArray(item.topics)) {
          const hasTopicMatch = item.topics.some(
            (topic) => topic.name && topic.name.toLowerCase().includes(searchLower),
          );
          if (hasTopicMatch) {
            matchedFields.push('topics');
          }
        }

        return {
          ...item,
          matchedFields,
        };
      });

      return {
        data,
        total,
      };
    } catch (error) {
      errorHandler(error);
      throw error;
    }
  },

  fetchListingForClient: async (
    state: FastifyStateI,
    { cursorDate, pageSize = 10, isVerified, type, search }: ForumQuestionListingInputI,
  ): Promise<{ data: ForumQuestionListingI[]; nextCursorDate: Date | null }> => {
    const selfProfileId = state.profileId;

    const filters: Prisma.Sql[] = [];
    const joins: Prisma.Sql[] = [
      Prisma.sql`
        INNER JOIN "user"."Profile" u ON q."profileId" = u."id" AND u."status" = 'ACTIVE'
        LEFT JOIN "network"."BlockedProfile" b1 ON b1."blockerId" = ${selfProfileId}::uuid AND b1."blockedId" = u."id"
        LEFT JOIN "network"."BlockedProfile" b2 ON b2."blockerId" = u."id" AND b2."blockedId" = ${selfProfileId}::uuid
        JOIN "forum"."Community" c ON q."communityId" = c."id"
      `,
    ];

    if (cursorDate) {
      filters.push(Prisma.sql`q."createdAt" < ${cursorDate}`);
    }

    if (isVerified !== undefined) {
      filters.push(isVerified ? Prisma.sql`va."id" IS NOT NULL` : Prisma.sql`va."id" IS NULL`);
    }

    if (type && type !== 'ALL') {
      filters.push(Prisma.sql`q."type" = ${type}::"forum"."QuestionTypeE"`);
    }

    if (search) {
      filters.push(Prisma.sql`(q."title" ILIKE ${'%' + search + '%'} OR q."description" ILIKE ${'%' + search + '%'})`);
    }

    filters.push(Prisma.sql`q."isLive" = true`);
    filters.push(
      Prisma.sql`(
        c."access" != 'PRIVATE'
        OR EXISTS (
          SELECT 1
          FROM "forum"."CommunityMember" cm
          WHERE cm."communityId" = c."id"
          AND cm."profileId" = ${selfProfileId}::uuid
        )
      )`,
    );

    filters.push(
      Prisma.sql`(
        c."isRestricted" = false
        OR EXISTS (
          SELECT 1
          FROM "forum"."CommunityMember" cm
          WHERE cm."communityId" = c."id"
          AND cm."profileId" = ${selfProfileId}::uuid
        )
      )`,
    );

    joins.push(Prisma.sql`
      LEFT JOIN "forum"."Answer" va ON va."questionId" = q."id" AND va."status" = 'VERIFIED_SOLUTION'
      LEFT JOIN "user"."Profile" vp ON va."profileId" = vp."id" AND vp."status" = 'ACTIVE'
      LEFT JOIN "forum"."QuestionMedia" qm ON qm."questionId" = q."id"
    `);

    const whereClause = filters.length ? Prisma.sql`WHERE ${Prisma.join(filters, ' AND ')}` : Prisma.empty;
    const joinClause = joins.length ? Prisma.join(joins, '\n') : Prisma.empty;

    const questions = await prismaPG.$queryRaw<FetchForumQuestionListingI[]>`
      WITH question_data AS (
        SELECT
          q."id",
          q."title",
          q."description" as "content",
          q."slug",
          q."createdAt",
          q."upvoteCount",
          q."downvoteCount",
          q."answerCount",
          CASE WHEN va."id" IS NOT NULL THEN true ELSE false END AS "isVerified",
             (
          SELECT json_agg(
            json_build_object(
              'id', qm."id",
              'fileUrl', qm."fileUrl",
              'fileExtension', qm."fileExtension"
            )
          )
          FROM "forum"."QuestionMedia" qm
          WHERE qm."questionId" = q."id"
        ) as "media",
          (
            SELECT json_agg(
              CASE
                WHEN qt."topicId" IS NOT NULL THEN json_build_object('id', t."id", 'name', t."name", 'type', 'master')
                ELSE json_build_object('id', tr."id", 'name', tr."name", 'type', 'raw')
              END
            )
            FROM "forum"."QuestionTopic" qt
            LEFT JOIN "forum"."Topic" t ON qt."topicId" = t."id"
            LEFT JOIN "rawData"."TopicRawData" tr ON qt."topicRawDataId" = tr."id"
            WHERE qt."questionId" = q."id"
          ) as "topics",
          CASE
            WHEN q."type" = 'TROUBLESHOOT' THEN json_build_object(
              'category', CASE
                WHEN q."equipmentCategoryId" IS NOT NULL THEN json_build_object('id', q."equipmentCategoryId", 'name', ec."name", 'type', 'master')
                WHEN q."equipmentCategoryRawDataId" IS NOT NULL THEN json_build_object('id', q."equipmentCategoryRawDataId", 'name', ecr."name", 'type', 'raw')
                ELSE NULL
              END,
              'manufacturer', CASE
                WHEN q."equipmentManufacturerId" IS NOT NULL THEN json_build_object('id', q."equipmentManufacturerId", 'name', emf."name", 'type', 'master')
                WHEN q."equipmentManufacturerRawDataId" IS NOT NULL THEN json_build_object('id', q."equipmentManufacturerRawDataId", 'name', emfr."name", 'type', 'raw')
                ELSE NULL
              END,
              'model', CASE
                WHEN q."equipmentModelId" IS NOT NULL THEN json_build_object('id', q."equipmentModelId", 'name', em."name", 'type', 'master')
                WHEN q."equipmentModelRawDataId" IS NOT NULL THEN json_build_object('id', q."equipmentModelRawDataId", 'name', emr."name", 'type', 'raw')
                ELSE NULL
              END
            )
            ELSE NULL
          END as "equipment",
          CAST(
  CASE
    WHEN va."id" IS NOT NULL THEN json_build_object(
      'content', va."text",
      'profile', json_build_object(
        'id', vp."id",
        'name', vp."name",
        'avatar', vp."avatar",
        'designation', vp."designationText",
        'entity', vp."entityText"
      )
    )
    ELSE NULL
  END AS json
) as "verifiedAnswer"
        FROM "forum"."Question" q
        ${joinClause}
        LEFT JOIN "ship"."EquipmentCategory" ec ON ec."id" = q."equipmentCategoryId"
        LEFT JOIN "rawData"."EquipmentCategoryRawData" ecr ON ecr."id" = q."equipmentCategoryRawDataId"
        LEFT JOIN "ship"."EquipmentModel" em ON em."id" = q."equipmentModelId"
        LEFT JOIN "rawData"."EquipmentModelRawData" emr ON emr."id" = q."equipmentModelRawDataId"
        LEFT JOIN "ship"."EquipmentManufacturer" emf ON emf."id" = q."equipmentManufacturerId"
        LEFT JOIN "rawData"."EquipmentManufacturerRawData" emfr ON emfr."id" = q."equipmentManufacturerRawDataId"
        ${whereClause}
        AND b1."blockerId" IS NULL
        AND b2."blockerId" IS NULL
         GROUP BY q."id", va."id", vp."id", ec."name", ecr."name", em."name", emr."name", emf."name", emfr."name"
        ORDER BY q."createdAt" DESC
        LIMIT ${pageSize}
      )
      SELECT * FROM question_data
    `;

    let nextCursorDate: Date | null = null;
    if (questions.length > 0) {
      nextCursorDate = questions[questions.length - 1].createdAt;
    }
    const data: ForumQuestionListingI[] = questions.map((q) => ({
      ...q,
      topics: q.topics || [],
      media: q.media || [],
      verifiedAnswer: q.verifiedAnswer
        ? {
            ...q.verifiedAnswer,
            content: selfProfileId ? q.verifiedAnswer.content : q.verifiedAnswer.content.slice(0, 100) + '...',
          }
        : undefined,
    }));

    return {
      data,
      nextCursorDate,
    };
  },
};
