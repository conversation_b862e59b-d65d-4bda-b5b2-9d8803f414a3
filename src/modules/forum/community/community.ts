import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type {
  // CommunityFetchOneForExternalClientResultI,
  FetchCommunityClientI,
} from '@interfaces/forum/community';
import { Prisma } from '@prisma/postgres';
import type {
  // ProfileIdRouteParamsI,
  RouteParamsI,
} from '@schemas/common/common';
import type {
  CommunityCreateOneI,
  CommunityFetchForClientI,
  CommunityUpdateOneI,
  //  CommunityOneForExternalClientParamsI
} from '@schemas/forum/community';
import type { GlobalSearchParamsI } from '@schemas/forum/question';
import type { GlobalSearchCommunityItemI, GlobalSearchResponseI } from '@interfaces/forum/search';
import { errorHandler } from '@utils/errors/handler';
import CommunityMemberModule from '../member';
import StorageModule from '@modules/storage';

export const CommunityModule = {
  isPermitted: async ({ communityId, profileId }: { communityId: string; profileId: string }): Promise<void> => {
    const communityResult = await CommunityModule.fetchById({ id: communityId });
    if (communityResult?.access === 'PRIVATE') {
      const _memberResult = await CommunityMemberModule.fetchMember({ communityId, profileId });
    }
    return;
  },
  fetchById: async (
    { id }: Pick<Prisma.CommunityWhereUniqueInput, 'id'>,
    select: Prisma.CommunitySelect = {
      access: true,
      isRestricted: true,
    },
  ) => {
    const communityResult = await prismaPG.community.findUnique({
      select,
      where: {
        id,
      },
    });
    if (!communityResult) {
      throw new AppError('CMTY007');
    }
    return communityResult;
  },
  createOne: async (
    state: FastifyStateI,
    { name, description, access, isRestricted, members, avatar }: CommunityCreateOneI,
  ) => {
    try {
      const selfProfileId = state.profileId;
      const result = await prismaPG.$transaction(async (txn) => {
        const creator = await txn.profile.findUnique({
          where: { id: selfProfileId },
          select: { name: true },
        });
        if (!creator) throw new AppError('PFL001');
        const communityInput: Prisma.CommunityUncheckedCreateInput = {
          name,
          description,
          access,
          isRestricted,
          creatorId: selfProfileId,
          memberCount: 1 + (members?.length ?? 0),
          CommunityMember: {
            create: {
              profileId: selfProfileId,
              type: 'ADMIN',
            },
            ...(members && members.length > 0
              ? {
                  createMany: {
                    data: members.map((member) => ({
                      profileId: member.profileId,
                      type: member.type,
                    })),
                    skipDuplicates: true,
                  },
                }
              : {}),
          },
        };
        if (avatar?.fileUrl) {
          communityInput.avatar = avatar.fileUrl;
        }
        const communityResult = await txn.community.create({
          data: communityInput,
          select: {
            id: true,
            cursorId: true,
            name: true,
            description: true,
            access: true,
            isRestricted: true,
            memberCount: true,
            questionCount: true,
            avatar: true,
            CommunityMember: {
              select: {
                profileId: true,
                type: true,
              },
            },
          },
        });
        if (!communityResult) {
          throw new AppError('CMTY001');
        }

        return {
          ...communityResult,
          cursorId: communityResult.cursorId.toString(),
        };
      });
      return result;
    } catch (error) {
      errorHandler(error);
    }
  },
  updateOne: async (
    state: FastifyStateI,
    { members, name, description, access, isRestricted, avatar }: CommunityUpdateOneI,
    { id: communityId }: RouteParamsI,
  ): Promise<FetchCommunityClientI> => {
    const profileId = state.profileId;
    const existingCommunityResult = await CommunityModule.fetchById(
      { id: communityId },
      {
        id: true,
        avatar: true,
      },
    );
    const isAdmin = await prismaPG.communityMember.findFirst({
      where: {
        communityId,
        profileId,
        type: 'ADMIN',
      },
    });
    if (!isAdmin) throw new AppError('CMTY004');

    if (avatar?.opr) {
      if (avatar.opr === 'UPDATE' && avatar?.fileUrl && existingCommunityResult.avatar) {
        try {
          await StorageModule.CoreStorageModule.deleteFile({ fileUrl: existingCommunityResult.avatar });
        } catch (_error) {
          // console.log(_error);
        }
      } else if (avatar.opr === 'DELETE' && existingCommunityResult.avatar) {
        try {
          await StorageModule.CoreStorageModule.deleteFile({ fileUrl: existingCommunityResult.avatar });
        } catch (_error) {
          // console.error(_error);
        }
      }
    }

    const updatedCommunity = await prismaPG.community.update({
      where: { id: communityId },
      data: {
        name,
        description,
        access,
        isRestricted,

        ...(avatar?.opr === 'CREATE' || avatar?.opr === 'UPDATE'
          ? { avatar: avatar?.fileUrl }
          : avatar?.opr === 'DELETE'
            ? { avatar: null }
            : {}),

        CommunityMember:
          members !== undefined
            ? {
                deleteMany: { profileId: { not: profileId } },
                ...(members.length > 0 && {
                  createMany: {
                    data: members.map((m) => ({
                      profileId: m.profileId,
                      type: m.type,
                    })),
                    skipDuplicates: true,
                  },
                }),
              }
            : undefined,

        ...(members !== undefined && {
          memberCount: members.length + 1,
        }),
      },
      include: {
        CommunityMember: {
          select: {
            profileId: true,
            type: true,
          },
        },
      },
    });

    return {
      ...updatedCommunity,
      cursorId: updatedCommunity.cursorId.toString(),
    };
  },
  deleteOne: async (state: FastifyStateI, filtersP: RouteParamsI) => {
    try {
      const profileId = state.profileId;
      const communityMemberResult = await prismaPG.communityMember.findFirst({
        where: {
          profileId: profileId,
          communityId: filtersP.id,
        },
      });
      if (communityMemberResult.type !== 'ADMIN') {
        throw new AppError('CMTY004');
      }
      // const adminListTemp = await prismaPG.communityMember.findMany({
      //   where:{
      //     communityId:filtersP.id,
      //     type:'ADMIN'
      //   },
      //   select:{
      //     profileId:true
      //   }
      // })
      // const adminList = adminListTemp.map(item => item.profileId)
      // if(adminList.length < 1){
      //   throw new AppError('CMTY005');
      // }
      const communityResult = await prismaPG.community.delete({
        where: { id: filtersP.id },
        select: { id: true },
      });

      if (!communityResult) {
        throw new AppError('CMTY003');
      }

      return communityResult;
    } catch (error) {
      errorHandler(error);
    }
  },
  fetchForClient: async ({ name, cursorId, pageSize }: CommunityFetchForClientI): Promise<FetchCommunityClientI[]> => {
    const searchName = name?.trim().toLowerCase() ?? '';

    if (typeof cursorId === 'number' && cursorId > 0) {
      cursorId = Number(cursorId);
    }
    const result: FetchCommunityClientI[] = await prismaPG.$queryRaw<FetchCommunityClientI[]>`
        SELECT
        c."id",
        c."cursorId"::text AS "cursorId",
        c."name",
        c."memberCount",
        c."questionCount",
        c."avatar",
        c."description"
        FROM forum."Community" c
        WHERE
          ${searchName ? Prisma.sql`c."name" ILIKE ${searchName + '%'}` : Prisma.sql`1 = 1`}
        ${cursorId ? Prisma.sql`AND c."cursorId" < ${cursorId}` : Prisma.empty}
      ORDER BY c."cursorId" DESC
      LIMIT ${pageSize}
      `;
    return result;
  },
  fetchRecommendedCommunities: async (
    state: FastifyStateI,
    days: number = 7,
    limit: number = 5,
  ): Promise<FetchCommunityClientI[]> => {
    const dateThreshold = new Date();
    dateThreshold.setDate(dateThreshold.getDate() - days);

    return prismaPG.$queryRaw<FetchCommunityClientI[]>`
      WITH community_scores AS (
        SELECT
          c.id,
          c.name,
          c."memberCount",
          c."questionCount",
          c."cursorId"::text,
          c.access,
          c."isRestricted",

          (

            (
              (COUNT(DISTINCT cm."communityId") * 0.3) +
              (COUNT(DISTINCT q.id) * 0.5) +
              (COUNT(DISTINCT a.id) * 0.2)
            ) * 0.6 +


            CASE
              WHEN MAX(q."createdAt") IS NULL THEN 0
              ELSE 0.4 * (
                1 - EXTRACT(EPOCH FROM (NOW() - MAX(q."createdAt"))) /
                EXTRACT(EPOCH FROM INTERVAL '${days} days')
              )
            END
          ) AS "recommendationScore"
        FROM forum."Community" c
        LEFT JOIN forum."CommunityMember" cm ON
          cm."communityId" = c.id
        LEFT JOIN forum."Question" q ON
          q."communityId" = c.id AND
          q."createdAt" >= ${dateThreshold}
        LEFT JOIN forum."Answer" a ON
          a."communityId" = c.id AND
          a."createdAt" >= ${dateThreshold}
        WHERE (
        c.access != 'PRIVATE'
        OR (c.access = 'PRIVATE' AND cm."profileId" = ${state.profileId})
       )
        GROUP BY c.id
      )
      SELECT
        id,
        "cursorId",
        name,
        "memberCount",
        "questionCount",
        access,
        "isRestricted"
      FROM community_scores
      WHERE "recommendationScore" > 0
      ORDER BY "recommendationScore" DESC
      LIMIT ${limit}
    `;
  },

  globalSearch: async (
    state: FastifyStateI,
    { search, page, pageSize }: GlobalSearchParamsI,
  ): Promise<GlobalSearchResponseI<GlobalSearchCommunityItemI>> => {
    const selfProfileId = state.profileId;
    const offset = page * pageSize;
    const searchTerm = `%${search.toLowerCase()}%`;

    // const tsSearchTerm = search.replace(/[!&|():*]/g, ' ').trim().split(/\s+/).join(' & ');

    try {
      const [totalResult, communitiesResult] = await Promise.all([
        prismaPG.$queryRaw<[{ count: bigint }]>`
          SELECT COUNT(DISTINCT c."id") as count
          FROM "forum"."Community" c
          LEFT JOIN "forum"."CommunityMember" cm ON c."id" = cm."communityId" AND cm."profileId" = ${selfProfileId}::uuid
          WHERE (
            (c."access" = 'PUBLIC' OR c."access" = 'GLOBAL')
            OR (c."access" = 'PRIVATE' AND cm."profileId" IS NOT NULL)
          )
          AND c."name" ILIKE ${searchTerm}
        `,
        prismaPG.$queryRaw<GlobalSearchCommunityItemI[]>`
          WITH ranked_communities AS (
            SELECT DISTINCT c."id",
            CASE
              WHEN c."name" ILIKE ${searchTerm} THEN 1
              ELSE 2
            END AS relevance_score,
            c."createdAt"
            FROM "forum"."Community" c
            LEFT JOIN "forum"."CommunityMember" cm ON c."id" = cm."communityId" AND cm."profileId" = ${selfProfileId}::uuid
            WHERE (
              (c."access" = 'PUBLIC' OR c."access" = 'GLOBAL')
              OR (c."access" = 'PRIVATE' AND cm."profileId" IS NOT NULL)
            )
            AND c."name" ILIKE ${searchTerm}
          )
          SELECT
            c."id",
            c."name",
            NULL as "description",
            c."access",
            c."isRestricted",
            c."memberCount",
            c."questionCount",
            ARRAY[]::text[] as "matchedFields"
          FROM "forum"."Community" c
          INNER JOIN ranked_communities rc ON c."id" = rc."id"
          ORDER BY rc.relevance_score ASC, rc."createdAt" DESC
          LIMIT ${pageSize}
          OFFSET ${offset}
        `,
      ]);

      const total = Number(totalResult?.[0]?.count || 0);

      const data = communitiesResult.map((item) => {
        const matchedFields: string[] = [];
        const searchLower = search.toLowerCase();

        if (item.name && item.name.toLowerCase().includes(searchLower)) {
          matchedFields.push('name');
        }

        return {
          ...item,
          matchedFields,
        };
      });

      return {
        data,
        total,
      };
    } catch (error) {
      errorHandler(error);
      throw error;
    }
  },
};
