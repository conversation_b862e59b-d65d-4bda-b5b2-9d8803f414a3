import { prismaPG } from '@config/db';
import { PostgresTxnI } from '@interfaces/common/db';
import { TopicClientI, TopicTransformParamsI } from '@interfaces/forum/topic';
import { IdTypeI, PaginationI } from '@schemas/common/common';
import { Prisma, Topic } from '@prisma/postgres';
import AppError from '@classes/AppError';
import { TopicFetchForClientI, TopicFetchsertI } from '@schemas/forum/topic';
import { PAGINATION } from '@consts/common/pagination';
import { DBDataTypeI } from '@consts/common/data';
import { IdNameTypeI, NullableI, TotalDataI } from '@interfaces/common/data';
import { MAX_TOPICS } from '@consts/forum/topic';

export const TopicModule = {
  fetchById: async ({ id, dataType }: IdTypeI, txn: PostgresTxnI = prismaPG): Promise<TopicClientI> => {
    const topicResultTemp = await txn.$queryRaw<Pick<Topic, 'id' | 'name'>>`
    ${
      dataType === 'master'
        ? Prisma.sql`
          SELECT
            t."id",
            t."name"
          FROM
            "forum"."Topic" t
          WHERE
            t."id" = ${id}::uuid
          LIMIT 1
        `
        : Prisma.sql`
          SELECT
            t."id",
            t."name"
          FROM
            "rawData"."TopicRawData" t
          WHERE
            t."id" = ${id}::uuid
          LIMIT 1
        `
    }
    `;
    if (!topicResultTemp) {
      throw new AppError('FMTP001');
    }
    return {
      ...topicResultTemp,
      dataType,
    } as TopicClientI;
  },
  fetchForClient: async (
    filtersP: TopicFetchForClientI,
    pagination: PaginationI = PAGINATION,
  ): Promise<TotalDataI<TopicClientI>> => {
    filtersP.search = filtersP.search?.trim()?.toLowerCase();

    const [topicResult, topicTotalResult] = await Promise.all([
      prismaPG.$queryRaw<TopicClientI[]>`
      SELECT * FROM
      (
        SELECT
          t."id",
          t."name",
          'master' AS "dataType"
        FROM
          "forum"."Topic" t
        ${filtersP.search ? Prisma.sql`WHERE t."name" ILIKE ${filtersP.search + '%'}` : Prisma.empty}

        UNION

        SELECT
          trw."id",
          trw."name",
          'raw' AS "dataType"
        FROM
          "rawData"."TopicRawData" trw
        ${filtersP.search ? Prisma.sql`WHERE trw."name" ILIKE ${filtersP.search + '%'}` : Prisma.empty}
      ) AS combinedResult
      ORDER BY
        combinedResult."dataType" ASC,
        combinedResult."name" ASC
     LIMIT ${pagination.pageSize}
    OFFSET ${pagination.page}
    `,
      prismaPG.$queryRaw<{ total: number }[]>`
              WITH topic AS (
                SELECT 1
                FROM
                "forum"."Topic" t
                ${filtersP.search ? Prisma.sql`WHERE t."name" ILIKE ${filtersP.search + '%'}` : Prisma.empty}
              ),
              topicRawData AS (
                SELECT 1
                FROM
                "rawData"."TopicRawData" trw
                ${filtersP.search ? Prisma.sql`WHERE trw."name" ILIKE ${filtersP.search + '%'}` : Prisma.empty}
              ),
              combined AS (
                SELECT * FROM topic
                UNION ALL
                SELECT * FROM topicRawData
              )
              SELECT COUNT(*)::INTEGER AS total FROM combined
      `,
    ]);
    return { data: topicResult, total: Number(topicTotalResult[0]?.total || 0) };
  },
  fetchsert: async ({ name }: TopicFetchsertI): Promise<TopicClientI> => {
    name = name?.toLowerCase()?.trim();
    const results = await prismaPG.$queryRaw<TopicClientI[]>`
    SELECT * FROM (
      SELECT
        t."id",
        t."name",
        'master' AS "dataType"
      FROM
        "forum"."Topic" t
      WHERE
        t."name" = ${name}

      UNION

      SELECT
        trw."id",
        trw."name",
        'raw' AS "dataType"
      FROM
        "rawData"."TopicRawData" trw
      WHERE
        trw."name" = ${name}
    ) AS combinedResult
    ORDER BY
      combinedResult."dataType" ASC,
      combinedResult."name" ASC
    LIMIT 1
  `;

    if (results && results.length > 0) {
      return results[0];
    }

    const topicResultTemp = await prismaPG.topicRawData.create({
      data: {
        name,
      },
      select: {
        id: true,
        name: true,
      },
    });

    if (!topicResultTemp) {
      throw new AppError('FMTP002', 'Failed to create topic');
    }

    return {
      ...topicResultTemp,
      dataType: 'raw' as DBDataTypeI,
    } as TopicClientI;
  },
  fetchExploreTopics: async (): Promise<TopicClientI[]> => {
    const result = await prismaPG.$queryRaw<TopicClientI[]>`
    SELECT * FROM (
      SELECT
        t."id",
        t."name",
        t."count",
        'master' AS "dataType"
      FROM
        "forum"."Topic" t

      UNION

      SELECT
        tr."id",
        tr."name",
        tr."count",
        'raw' AS "dataType"
      FROM
        "rawData"."TopicRawData" tr
    ) AS combinedResult
    ORDER BY combinedResult."count" DESC, combinedResult."name" ASC
    LIMIT ${MAX_TOPICS};
  `;
    return result;
  },

  transform: ({
    topicId,
    topicName,
    topicRawDataId,
    topicRawDataName,
  }: TopicTransformParamsI): NullableI<IdNameTypeI> =>
    topicId
      ? { id: topicId, name: topicName, type: 'master' }
      : topicRawDataId
        ? { id: topicRawDataId, name: topicRawDataName, type: 'raw' }
        : null,
};
