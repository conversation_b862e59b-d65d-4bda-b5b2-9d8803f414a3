import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { PrismaErrorCodes } from '@consts/common/error/prismaErrorCodes';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type {
  ProfileFetchAboutResultI,
  ProfileFetchAboutResultTempI,
  ProfileFetchBioI,
  ProfileFetchDataI,
  ProfileIdentityI,
  ProfileInstitutionI,
  ProfileStatutoryCertI,
  ProfileUpdateOneDataI,
} from '@interfaces/user/profile';
import type { BasicProfilesParamsI, BasicProfilesResultI } from '@interfaces/user/basics';
import Company from '@modules/company';
import { Prisma } from '@prisma/postgres';
import type { Profile, ProfileStatus } from '@prisma/postgres';
import type {
  CreateOnBoardingPersonalParamsI,
  CreateOnBoardingWorkParamsI,
  UpdateBioParamsI,
} from '@schemas/user/profile';
import ProfileStatusModule from './profileStatus';
import type { IdTypeI, RouteParamsI } from '@schemas/common/common';
import { NON_ENTITY_EMPLOYEE_DESIGNATION_IDS } from '@consts/master/designation';
import { isFilled } from '@utils/data/object';
import { DegreeNestedClientI } from '@interfaces/company/degree';
import { EntityNestedExternalI } from '@interfaces/company/entity';
import { SkillNestedExternalI } from '@interfaces/career/skill';
import { getCurrentDate } from '@utils/data/date';

const ProfileModule = {
  fetchData: async (filters: RouteParamsI) => {
    const profileResult = await prismaPG.profile.findUnique({
      select: {
        username: true,
        name: true,
        email: true,
        id: true,
        designationText: true,
        entityText: true,
        avatar: true,
        DesignationAlternative: {
          select: {
            id: true,
            name: true,
          },
        },
        DesignationRawData: {
          select: {
            id: true,
            name: true,
          },
        },
        Entity: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        EntityRawData: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        ProfileStatus: {
          select: {
            isEmailVerified: true,
            isPersonalDetailsSaved: true,
            isWorkDetailsSaved: true,
            isPrivacyPolicyAccepted: true,
            profileId: true,
          },
        },
      },
      where: {
        id: filters.id,
        status: 'ACTIVE',
      },
    });
    if (!profileResult) {
      throw new AppError('PFL001');
    }
    const profileFetchDataResult: ProfileFetchDataI = {
      email: profileResult.email,
      name: profileResult.name,
      username: profileResult.username,
      designationText: profileResult.designationText,
      entityText: profileResult.entityText,
      avatar: profileResult.avatar,

      isEmailVerified: profileResult.ProfileStatus.isEmailVerified,
      isPersonalDetailsSaved: profileResult.ProfileStatus.isPersonalDetailsSaved,
      isWorkDetailsSaved: profileResult.ProfileStatus.isWorkDetailsSaved,
      isPrivacyPolicyAccepted: profileResult.ProfileStatus.isPrivacyPolicyAccepted,
      profileId: profileResult.ProfileStatus.profileId,
      designation: isFilled(profileResult?.DesignationAlternative)
        ? {
            id: profileResult?.DesignationAlternative.id,
            name: profileResult?.DesignationAlternative.name,
            dataType: 'master',
          }
        : isFilled(profileResult?.DesignationRawData)
          ? {
              id: profileResult?.DesignationRawData.id,
              name: profileResult?.DesignationRawData.name,
              dataType: 'raw',
            }
          : null,
      entity: isFilled(profileResult?.Entity)
        ? {
            id: profileResult?.Entity.id,
            name: profileResult?.Entity.name,
            type: profileResult?.Entity.type,
            dataType: 'master',
          }
        : isFilled(profileResult?.EntityRawData)
          ? {
              id: profileResult?.EntityRawData.id,
              name: profileResult?.EntityRawData.name,
              type: profileResult?.EntityRawData.type,
              dataType: 'raw',
            }
          : null,
    };

    return profileFetchDataResult;
  },
  fetchBasic: async (filters: Pick<Prisma.ProfileWhereUniqueInput, 'id'>) => {
    const profileResult = await prismaPG.profile.findUnique({
      select: {
        email: true,
        name: true,
        username: true,
        designationText: true,
        entityText: true,
        followersCount: true,
        avatar: true,
      },
      where: {
        id: filters.id,
        status: 'ACTIVE',
      },
    });

    if (!profileResult) {
      throw new AppError('PFL001');
    }
    return profileResult;
  },
  // fetchBasicProfiles: async (filters: Pick<Prisma.ProfileWhereUniqueInput, 'id'>) => {
  //   const profileResult = await prismaPG.profile.findUnique({
  //     select: {
  //       email: true,
  //       name: true,
  //       username: true,
  //       designationText: true,
  //       entityText: true,
  //       followersCount: true,
  //       avatar: true,
  //     },
  //     where: {
  //       id: filters.id,
  //     },
  //   });

  //   if (!profileResult) {
  //     throw new AppError('PFL001');
  //   }
  //   return profileResult;
  // },
  fetchBasicProfiles: async (params: BasicProfilesParamsI): Promise<BasicProfilesResultI> => {
    const profiles = await prismaPG.profile.findMany({
      where: {
        id: {
          in: params.profileIds,
        },
        status: 'ACTIVE',
      },
      select: {
        id: true,
        name: true,
        avatar: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    if (!profiles.length) {
      throw new AppError('PFL001');
    }

    return profiles.map((profile) => ({
      id: profile.id,
      name: profile.name,
      avatar: profile.avatar,
    }));
  },

  fetchBio: async (filters: RouteParamsI): Promise<ProfileFetchBioI> => {
    const profileResult = await prismaPG.profile.findUnique({
      select: {
        name: true,
        designationAlternativeId: true,
        designationRawDataId: true,
        designationText: true,
        entityId: true,
        entityRawDataId: true,
        entityText: true,
        description: true,
        DesignationAlternative: {
          select: {
            id: true,
            name: true,
          },
        },
        DesignationRawData: {
          select: {
            id: true,
            name: true,
          },
        },
        Entity: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        EntityRawData: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      where: {
        id: filters.id,
        status: 'ACTIVE',
      },
    });

    if (!profileResult) {
      throw new AppError('PFL001');
    }
    const profileExternalResult: ProfileFetchBioI = {
      name: profileResult.name,
      designationText: profileResult.designationText,
      entityText: profileResult.entityText,
      description: profileResult.description,
      designation: isFilled(profileResult?.DesignationAlternative)
        ? {
            id: profileResult?.DesignationAlternative.id,
            name: profileResult?.DesignationAlternative.name,
            dataType: 'master',
          }
        : isFilled(profileResult?.DesignationRawData)
          ? {
              id: profileResult?.DesignationRawData.id,
              name: profileResult?.DesignationRawData.name,
              dataType: 'raw',
            }
          : null,
      entity: isFilled(profileResult?.Entity)
        ? {
            id: profileResult?.Entity.id,
            name: profileResult?.Entity.name,
            type: profileResult?.Entity.type,
            dataType: 'master',
          }
        : isFilled(profileResult?.EntityRawData)
          ? {
              id: profileResult?.EntityRawData.id,
              name: profileResult?.EntityRawData.name,
              type: profileResult?.EntityRawData.type,
              dataType: 'raw',
            }
          : null,
    };
    return profileExternalResult;
  },
  fetchAbout: async (state: FastifyStateI, { id }: RouteParamsI): Promise<ProfileFetchAboutResultI> => {
    const selfProfileId = state.profileId;
    const profileResultTempResult = await prismaPG.$queryRaw<ProfileFetchAboutResultTempI[]>`
    WITH userData AS (
      SELECT
        u."id" as "profileId",
        u."username" as "username",
        u."name" as "name",
        u."avatar" as "avatar",
        u."designationText" as "designationText",
        u."designationAlternativeId" as "designationAlternativeId",
        u."designationRawDataId" as "designationRawDataId",
        u."entityText" as "entityText",
        u."entityId" as "entityId",
        u."entityRawDataId" as "entityRawDataId",
        u."followersCount" as "followersCount",
        u."followingsCount" as "followingsCount",
        u."description" as "description",
        EXISTS (
          SELECT 1
          FROM "network"."Follow" f
          WHERE f."followerProfileId" = ${selfProfileId}::uuid
          AND f."followeeProfileId" = u."id"
        ) AS "isFollowing",
        json_build_object(
          'status',
          CASE
            WHEN c."profileId" = ${selfProfileId}::uuid
            AND c."connectedId" = u."id"::uuid
            THEN 'CONNECTED'
            WHEN sr."status" = 'PENDING' THEN 'PENDING'
            WHEN rr."status" = 'PENDING' THEN 'PENDING'
            ELSE NULL
          END,
          'senderRequestId',
          CASE
            WHEN sr."status" = 'PENDING' THEN ${selfProfileId}::uuid
            WHEN rr."status" = 'PENDING' THEN ${id}::uuid
            ELSE NULL
          END
        ) AS "request"
      FROM "user"."Profile" u
      LEFT JOIN "network"."Request" sr
        ON sr."senderProfileId" = ${selfProfileId}::uuid
        AND sr."receiverProfileId" = u."id"::uuid
      LEFT JOIN "network"."Connection" c
        ON c."profileId" = ${selfProfileId}::uuid
        AND c."connectedId" = u."id"::uuid
      LEFT JOIN "network"."Request" rr
        ON rr."receiverProfileId" = ${selfProfileId}::uuid
        AND rr."senderProfileId" = u."id"::uuid
      WHERE u."id" = ${id}::uuid
      AND u."status" = 'ACTIVE'::"user"."ProfileStatusE"
      LIMIT 1
    ),
    institutions AS (
      SELECT
        pe."id",
        pe."profileId",
        pe."entityId",
        pe."entityRawDataId",
        pe."degreeId",
        pe."degreeRawDataId",
        pe."fromDate",
        pe."toDate",
        pe."createdAt",
        e."name" as "entityName",
        er."name" as "entityRawDataName",
        d."name" as "degreeName",
        dr."name" as "degreeRawDataName"
      FROM "career"."ProfileEducation" pe
      LEFT JOIN "company"."Entity" e
        ON e."id" = pe."entityId"
      LEFT JOIN "rawData"."EntityRawData" er
        ON er."id" = pe."entityRawDataId"
      LEFT JOIN "company"."Degree" d
        ON d."id" = pe."degreeId"
      LEFT JOIN "rawData"."DegreeRawData" dr
        ON dr."id" = pe."degreeRawDataId"
      WHERE pe."profileId" = ${id}::uuid
      ORDER BY pe."toDate" DESC NULLS FIRST, pe."fromDate" DESC, pe."createdAt" DESC
      LIMIT 3
    ),
    statutoryCerts AS (
      SELECT
        sc."id" AS "id",
        sc."entityId" AS "entityId",
        sc."entityRawDataId" AS "entityRawDataId",
        e."name" AS "entityName",
        er."name" AS "entityRawDataName",
        sc."certificateCourseId" AS "certificateCourseId",
        c."name" AS "certificateCourseName",
        sc."certificateCourseRawDataId" AS "certificateCourseRawDataId",
        cr."name" AS "certificateCourseRawDataName",
        sc."fromDate" AS "fromDate",
        sc."untilDate" AS "untilDate"
      FROM "career"."ProfileCertificate" sc
      LEFT JOIN "company"."Entity" e
        ON e."id" = sc."entityId"
      LEFT JOIN "rawData"."EntityRawData" er
        ON er."id" = sc."entityRawDataId"
      LEFT JOIN "company"."CertificateCourse" c
        ON c."id" = sc."certificateCourseId"
        AND c."type" = 'STATUTORY'::"company"."CertificateCourseTypeE"
      LEFT JOIN "rawData"."CertificateCourseRawData" cr
        ON cr."id" = sc."certificateCourseRawDataId"
        AND cr."type" = 'STATUTORY'::"company"."CertificateCourseTypeE"
      WHERE sc."profileId" = ${id}::uuid
      AND (
        (sc."certificateCourseId" IS NOT NULL AND c."id" IS NOT NULL) OR
        (sc."certificateCourseRawDataId" IS NOT NULL AND cr."id" IS NOT NULL)
      )
      ORDER BY sc."untilDate" DESC NULLS FIRST, sc."fromDate" DESC, sc."createdAt" DESC
      LIMIT 3
    ),
    identities AS (
      SELECT
        i."id",
        i."type",
        i."fromDate",
        i."untilDate",
        c."name" AS "countryName"
      FROM "document"."Identity" i
      INNER JOIN "master"."Country" c
        ON c."iso2" = i."countryIso2"
      WHERE i."profileId" = ${id}::uuid
      ORDER BY i."untilDate" DESC NULLS FIRST, i."fromDate" DESC, i."createdAt" DESC
      LIMIT 3
    ),
    maritimeSkills AS (
      SELECT
        ps."id",
        ps."skillId",
        ps."skillRawDataId",
        s."name" AS "skillName",
        sr."name" AS "skillRawDataName"
      FROM "career"."ProfileSkill" ps
      LEFT JOIN "company"."Skill" s
        ON s."id" = ps."skillId"
      LEFT JOIN "rawData"."SkillRawData" sr
        ON sr."id" = ps."skillRawDataId"
      WHERE ps."profileId" = ${id}::uuid
      AND (
        (ps."skillId" IS NOT NULL AND s."category" = 'MARITIME'::"company"."SkillCategoryE") OR
        (ps."skillRawDataId" IS NOT NULL AND sr."category" = 'MARITIME'::"rawData"."SkillCategoryRawDataE")
      )
      ORDER BY ps."createdAt" DESC
      LIMIT 3
    ),
    mutuals AS (
      SELECT
        u."avatar" as "avatar"
      FROM "network"."Connection" c1
      INNER JOIN "network"."Connection" c2
        ON c1."connectedId" = c2."connectedId"
      INNER JOIN "user"."Profile" u
        ON u."id" = c1."connectedId"
      WHERE
        c1."profileId" = ${selfProfileId}::uuid
        AND c2."profileId" = ${id}::uuid
      ORDER BY c1."createdAt" DESC
      LIMIT 3
    ),
    metaData AS (
      SELECT
        m."educationCount" AS "educationCount",
        m."statutoryCertCount" AS "statutoryCertCount",
        m."valueAddedCertCount" AS "valueAddedCertCount",
        m."identityCount" AS "identityCount",
        m."visaCount" AS "visaCount"
      FROM "user"."ProfileMeta" m
      WHERE m."profileId" = ${id}::uuid
    ),
    mutualTotal AS (
      SELECT
        COUNT(*)::int AS "total"
      FROM "network"."Connection" c1
      INNER JOIN "network"."Connection" c2
        ON c1."connectedId" = c2."connectedId"
      WHERE c1."profileId" = ${selfProfileId}::uuid
      AND c2."profileId" = ${id}::uuid
    ),
    maritimeSkillsTotal AS (
      SELECT
        COUNT(*)::int AS "total"
      FROM "career"."ProfileSkill" ps
      LEFT JOIN "company"."Skill" s
        ON s."id" = ps."skillId"
      LEFT JOIN "rawData"."SkillRawData" sr
        ON sr."id" = ps."skillRawDataId"
      WHERE ps."profileId" = ${id}::uuid
      AND (
        (ps."skillId" IS NOT NULL AND s."category" = 'MARITIME'::"company"."SkillCategoryE") OR
        (ps."skillRawDataId" IS NOT NULL AND sr."category" = 'MARITIME'::"rawData"."SkillCategoryRawDataE")
      )
    ),
    otherSkillsTotal AS (
      SELECT
          COUNT(*)::int AS "total"
      FROM "career"."ProfileSkill" ps
      LEFT JOIN "company"."Skill" s ON s."id" = ps."skillId"
      LEFT JOIN "rawData"."SkillRawData" sr ON sr."id" = ps."skillRawDataId"
      WHERE ps."profileId" = ${id}::uuid
      AND (
          (ps."skillId" IS NOT NULL AND s."category" != 'MARITIME'::"company"."SkillCategoryE") OR
          (ps."skillRawDataId" IS NOT NULL AND sr."category" != 'MARITIME'::"rawData"."SkillCategoryRawDataE")
      )
    )
    SELECT
      ud.*,
      (
        SELECT json_build_object(
          'educationCount', m."educationCount",
          'statutoryCertCount', m."statutoryCertCount",
          'valueAddedCertCount', m."valueAddedCertCount",
          'identityCount', m."identityCount",
          'visaCount', m."visaCount",
          'maritimeSkillsTotal', (SELECT s."total" FROM maritimeSkillsTotal s),
          'otherSkillsTotal', (SELECT s."total" FROM otherSkillsTotal s),
          'mutualTotal', (SELECT mt."total" FROM mutualTotal mt)
        )
        FROM metaData m
      ) AS "meta",
      (SELECT json_agg(muts) FROM mutuals muts) AS "mutuals",
      (SELECT json_agg(ins) FROM institutions ins) AS "institutions",
      (SELECT json_agg(idt) FROM identities idt) AS "identities",
      (SELECT json_agg(sc) FROM statutoryCerts sc) AS "statutoryCerts",
      (SELECT json_agg(ms) FROM maritimeSkills ms) AS "maritimeSkills"
    FROM userData ud;`;

    if (!profileResultTempResult?.length) {
      throw new AppError('PFL001');
    }

    const profileResultTemp = profileResultTempResult[0];

    const profileResult: ProfileFetchAboutResultI = {
      profileId: profileResultTemp.profileId,
      username: profileResultTemp.username,
      name: profileResultTemp.name,
      avatar: profileResultTemp.avatar,
      followersCount: profileResultTemp.followersCount,
      followingsCount: profileResultTemp.followingsCount,
      description: profileResultTemp.description,
      isFollowing: profileResultTemp.isFollowing,
      designation: profileResultTemp?.designationAlternativeId
        ? {
            id: profileResultTemp.designationAlternativeId,
            name: profileResultTemp.designationText!,
            dataType: 'master',
          }
        : profileResultTemp?.designationRawDataId
          ? {
              id: profileResultTemp.designationRawDataId,
              name: profileResultTemp.designationText!,
              dataType: 'raw',
            }
          : null,
      entity: profileResultTemp?.entityId
        ? {
            id: profileResultTemp.entityId,
            name: profileResultTemp.entityText!,
            dataType: 'master',
          }
        : profileResultTemp?.entityRawDataId
          ? {
              id: profileResultTemp.entityRawDataId,
              name: profileResultTemp.entityText!,
              dataType: 'raw',
            }
          : null,
      request: profileResultTemp.request,
      meta: profileResultTemp.meta,
      institutions:
        profileResultTemp.institutions?.map(
          (item) =>
            ({
              id: item.id,
              fromDate: item.fromDate,
              toDate: item.toDate,
              degree: item?.degreeId
                ? ({
                    id: item.degreeId,
                    name: item.degreeName!,
                    dataType: 'master',
                  } as DegreeNestedClientI)
                : item?.degreeRawDataId
                  ? ({
                      id: item.degreeRawDataId,
                      name: item.degreeRawDataName!,
                      dataType: 'raw',
                    } as DegreeNestedClientI)
                  : null,
              entity: item?.entityId
                ? ({
                    id: item.entityId,
                    name: item.entityName!,
                    dataType: 'master',
                  } as EntityNestedExternalI)
                : item?.entityRawDataId
                  ? ({
                      id: item.entityRawDataId,
                      name: item.entityRawDataName!,
                      dataType: 'raw',
                    } as EntityNestedExternalI)
                  : null,
            }) as ProfileInstitutionI,
        ) ?? [],
      statutoryCerts:
        profileResultTemp.statutoryCerts?.map(
          (item) =>
            ({
              id: item.id,
              name: item.certificateCourseName || item.certificateCourseRawDataName || '',
              fromDate: item.fromDate,
              untilDate: item.untilDate,
              entity: item?.entityId
                ? ({
                    id: item.entityId,
                    name: item.entityName!,
                    dataType: 'master',
                  } as EntityNestedExternalI)
                : item?.entityRawDataId
                  ? ({
                      id: item.entityRawDataId,
                      name: item.entityRawDataName!,
                      dataType: 'raw',
                    } as EntityNestedExternalI)
                  : null,
            }) as ProfileStatutoryCertI,
        ) ?? [],
      identities:
        profileResultTemp.identities?.map(
          (item) =>
            ({
              id: item.id,
              type: item.type,
              countryName: item.countryName,
              fromDate: item.fromDate,
              untilDate: item.untilDate,
            }) as ProfileIdentityI,
        ) ?? [],
      maritimeSkills:
        profileResultTemp.maritimeSkills?.map(
          (item) =>
            (item?.skillId
              ? {
                  id: item.skillId,
                  name: item.skillName!,
                  dataType: 'master',
                }
              : item?.skillRawDataId
                ? {
                    id: item.skillRawDataId,
                    name: item.skillRawDataName!,
                    dataType: 'raw',
                  }
                : null) as SkillNestedExternalI,
        ) ?? [],
      mutuals: profileResultTemp.mutuals ?? [],
    };

    return profileResult;
  },
  isUnusedEmail: async (filters: Pick<Prisma.ProfileWhereUniqueInput, 'email'>): Promise<void> => {
    const existingProfileResult = await prismaPG.profile.findFirst({
      select: { email: true },
      where: {
        email: filters.email,
        status: {
          notIn: ['DELETED'],
        },
      },
    });
    if (existingProfileResult) {
      throw new AppError('PFL007');
    }
    return;
  },
  isUnusedUsername: async (filters: Pick<Prisma.ProfileWhereUniqueInput, 'username'>): Promise<void> => {
    const existingProfileResult = await prismaPG.profile.findFirst({
      select: { username: true },
      where: {
        username: {
          equals: filters.username.toString(),
          mode: 'insensitive',
        },
        status: {
          notIn: ['DELETED'],
        },
      },
    });
    if (existingProfileResult) {
      throw new AppError('PFL006');
    }
    return;
  },
  updateOne: async (
    params: ProfileUpdateOneDataI,
    filters: Pick<Prisma.ProfileWhereUniqueInput, 'id'>,
    select: Prisma.ProfileSelect = {
      id: true,
    },
  ): Promise<Profile> => {
    try {
      if (params?.designationAlternativeId && String(params?.designationAlternativeId)?.length) {
        params.designationRawDataId = null;
      } else if (params?.designationRawDataId && String(params?.designationRawDataId)?.length) {
        params.designationAlternativeId = null;
      }
      if (params?.entityId && String(params?.entityId)?.length) {
        params.entityRawDataId = null;
      } else if (params?.entityRawDataId && String(params?.entityRawDataId)?.length) {
        params.entityId = null;
      }

      if (params?.username && String(params?.username)?.length) {
        await ProfileModule.isUnusedUsername({
          username: String(params.username),
        });
      }
      if (params?.email && String(params?.email)?.length) {
        await ProfileModule.isUnusedUsername({
          username: String(params.username),
        });
      }
      const profileResult = await prismaPG.profile.update({
        data: params,
        where: {
          id: filters.id,
        },
        select,
      });

      if (!profileResult?.id) {
        throw new AppError('PFL003');
      }
      return profileResult;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === PrismaErrorCodes.RECORD_NOT_FOUND) {
          throw new AppError('PFL003');
        }
      }
      throw error;
    }
  },
  updateBio: async (params: UpdateBioParamsI, profileId: string): Promise<unknown> => {
    const entity = params.entity as IdTypeI;
    const designation = params.designation as IdTypeI;

    const [entityResult, designationResult] = await Promise.all([
      entity?.id ? Company.EntityModule.fetchById(entity) : null,
      designation?.id ? Company.DesignationModule.fetchById(designation) : null,
    ]);

    const profileUpdateParams: ProfileUpdateOneDataI = {};

    if (params.name) {
      profileUpdateParams.name = params.name;
    }

    if (params.description) {
      profileUpdateParams.description = params.description;
    }

    profileUpdateParams.avatar = params.avatar;

    if (entityResult?.id) {
      profileUpdateParams.entityText = entityResult.name;
      if (entityResult.dataType !== 'raw') {
        profileUpdateParams.entityId = entityResult.id;
        profileUpdateParams.entityRawDataId = null;
      } else {
        profileUpdateParams.entityRawDataId = entityResult.id;
        profileUpdateParams.entityId = null;
      }
    }

    if (designationResult?.id) {
      profileUpdateParams.designationText = designationResult.name;
      if (designationResult.dataType !== 'raw') {
        profileUpdateParams.designationAlternativeId = designationResult.id;
        profileUpdateParams.designationRawDataId = null;
      } else {
        profileUpdateParams.designationRawDataId = designationResult.id;
        profileUpdateParams.designationAlternativeId = null;
      }
    }

    return await prismaPG.profile.update({
      data: profileUpdateParams,
      where: { id: profileId, status: 'ACTIVE' },
      select: { id: true },
    });
  },
  upsertOnBoardingPersonal: async (
    state: FastifyStateI,
    params: CreateOnBoardingPersonalParamsI,
  ): Promise<Pick<ProfileStatus, 'isPersonalDetailsSaved' | 'profileId'>> => {
    const _profileResult = await ProfileModule.updateOne(
      { name: params.fullName, gender: params.gender, countryIso2: params.countryIso2 },
      { id: state.profileId },
      { id: true, name: true, gender: true, countryIso2: true },
    );
    const profileStatusResult = await ProfileStatusModule.updateOne(
      { isPersonalDetailsSaved: true },
      { profileId: state.profileId },
      { isPersonalDetailsSaved: true, profileId: true },
    );
    return profileStatusResult;
  },
  upsertOnBoardingWork: async (
    state: FastifyStateI,
    params: CreateOnBoardingWorkParamsI,
  ): Promise<Pick<ProfileStatus, 'isWorkDetailsSaved' | 'profileId'>> => {
    const designation = params.designation as IdTypeI;

    const entity = params.entity as IdTypeI;
    if (!designation.id?.length) {
      throw new AppError('PFL005');
    }
    // If entity wasn't passed and the designation wasn't a non maritime designation then we'll throw an error
    else if (!entity?.id?.length && !NON_ENTITY_EMPLOYEE_DESIGNATION_IDS.has(designation.id)) {
      throw new AppError('PFL005');
    }
    const [designationResult, entityResult] = await Promise.all([
      Company.DesignationModule.fetchById(designation),
      entity?.id?.length ? Company.EntityModule.fetchById(entity) : null,
    ]);

    const toUpdateParams: ProfileUpdateOneDataI = {};
    switch (designationResult.dataType) {
      case 'raw': {
        toUpdateParams.designationText = designationResult.name;
        toUpdateParams.designationRawDataId = designationResult.id;
        toUpdateParams.designationAlternativeId = null;
        break;
      }
      case 'master': {
        toUpdateParams.designationText = designationResult.name;
        toUpdateParams.designationAlternativeId = designationResult.id;
        toUpdateParams.designationRawDataId = null;
        break;
      }
    }
    if (entityResult?.id?.length) {
      switch (entityResult.dataType) {
        case 'raw': {
          toUpdateParams.entityText = entityResult.name;
          toUpdateParams.entityRawDataId = entityResult.id;
          toUpdateParams.entityId = null;
          break;
        }
        case 'master': {
          toUpdateParams.entityText = entityResult.name;
          toUpdateParams.entityId = entityResult.id;
          toUpdateParams.entityRawDataId = null;
          break;
        }
      }
    }
    const _profileResult = await ProfileModule.updateOne(toUpdateParams, { id: state.profileId });

    const profileStatusResult = await ProfileStatusModule.updateOne(
      { isWorkDetailsSaved: true },
      { profileId: state.profileId },
      { isWorkDetailsSaved: true, profileId: true },
    );
    return profileStatusResult;
  },
  transformProfile: (profile) => ({
    id: profile.id,
    name: profile.name,
    avatar: profile.avatar,
    designation: profile.designationAlternativeId
      ? {
          id: profile.designationAlternativeId,
          name: profile.designationText || '',
          dataType: 'master' as const,
        }
      : profile.designationRawDataId
        ? {
            id: profile.designationRawDataId,
            name: profile.designationText || '',
            dataType: 'raw' as const,
          }
        : null,
    entity: profile.entityId
      ? {
          id: profile.entityId,
          name: profile.entityText || '',
          dataType: 'master' as const,
        }
      : profile.entityRawDataId
        ? {
            id: profile.entityRawDataId,
            name: profile.entityText || '',
            dataType: 'raw' as const,
          }
        : null,
  }),
  deactivate: async (state: FastifyStateI): Promise<void> => {
    const selfProfileId = state.profileId;
    let profileResult = await prismaPG.profile.findUnique({
      where: {
        id: selfProfileId,
      },
      select: {
        status: true,
      },
    });
    if (profileResult?.status !== 'ACTIVE') {
      throw new AppError('PFL015');
    }
    profileResult = await prismaPG.profile.update({ data: { status: 'INACTIVE' }, where: { id: selfProfileId } });
    return;
  },
  deleteOne: async (state: FastifyStateI): Promise<void> => {
    const selfProfileId = state.profileId;
    const profileResult = await prismaPG.profile.findUnique({
      where: {
        id: selfProfileId,
      },
      select: {
        id: true,
        status: true,
      },
    });
    if (!profileResult) {
      throw new AppError('PFL001');
    }
    if (profileResult.status === 'SCHEDULED_FOR_DELETION') {
      throw new AppError('PFL013');
    }
    if (profileResult.status === 'DELETED') {
      throw new AppError('PFL014');
    }
    const deletionInitiatedAt = getCurrentDate();
    await prismaPG.$transaction([
      prismaPG.profile.update({
        where: {
          id: selfProfileId,
        },
        data: {
          status: 'SCHEDULED_FOR_DELETION',
        },
      }),
      prismaPG.profileStatus.update({
        where: {
          profileId: state.profileId,
        },
        data: {
          deletionInitiatedAt,
        },
      }),
    ]);
  },
  softDeleteProfiles: async (): Promise<void> => {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const profiles = await prismaPG.profile.findMany({
      where: {
        status: 'SCHEDULED_FOR_DELETION',
        ProfileStatus: {
          deletionInitiatedAt: {
            lte: thirtyDaysAgo,
          },
        },
      },
      select: {
        id: true,
      },
    });
    const profilesToDelete = profiles?.map((profile) => profile.id);
    if (!profilesToDelete?.length) {
      return;
    }

    await prismaPG.profile.updateMany({
      where: {
        id: { in: profilesToDelete },
      },
      data: {
        status: 'DELETED',
      },
    });
  },
};
export default ProfileModule;
