import { SessionUpdateCoordinatesParamsSchema } from '@schemas/auth/session';
import { CursorPaginationSchema } from '@schemas/common/common';
import z from 'zod';

export const NearByFetchPeopleBodySchema = SessionUpdateCoordinatesParamsSchema;

export const NearByFetchPeopleParamsSchema = CursorPaginationSchema.merge(NearByFetchPeopleBodySchema);

export type NearByFetchPeopleParamsI = z.infer<typeof NearByFetchPeopleParamsSchema>;
