import AppError from '@classes/AppError';
import { CountryIso2Schema, IdTypeSchema } from '@schemas/common/common';
import { DateSchema, Time24Schema } from '@schemas/common/date';
import { CityMapboxSchema } from '@schemas/master/city';
import { LatitudeSchema, LongitudeSchema } from '@schemas/port/common';
import { AddressRawDataMapboxSchema } from '@schemas/rawData/addressRawData';
import { getCurrentDate, isLessThanEqualDateRange } from '@utils/data/date';
import { isEmpty, isFilled } from '@utils/data/object';
import z from 'zod';

export const AnnouncementCreateOneSchema = z
  .object({
    title: z.string().min(3).max(100),
    description: z.string().min(3).max(1000),
    latitude: LatitudeSchema,
    longitude: LongitudeSchema,
    addressMapBox: AddressRawDataMapboxSchema.optional(),
    city: IdTypeSchema.optional(),
    cityMapbox: CityMapboxSchema.optional(),
    countryIso2: CountryIso2Schema,
    startDate: DateSchema,
    endDate: DateSchema,
    startTime: Time24Schema,
    endTime: Time24Schema,
  })
  .superRefine((data, _ctx) => {
    if (isEmpty(data?.city) && isEmpty(data?.cityMapbox)) {
      throw new AppError('ANC001');
    }
    if (isEmpty(data?.addressMapBox)) {
      if (isFilled(data?.cityMapbox)) {
        throw new AppError('ANC002');
      }
    }
    if (!isLessThanEqualDateRange(data.startDate, data.endDate)) {
      throw new AppError('ANC003');
    }
    const currentDate = getCurrentDate();
    if (currentDate > data.startDate || currentDate > data.endDate) {
      throw new AppError('ANC004');
    }
  });
export type AnnouncementCreateOneI = z.infer<typeof AnnouncementCreateOneSchema>;
