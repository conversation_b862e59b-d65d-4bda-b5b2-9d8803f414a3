import AppError from '@classes/AppError';
import { CountryIso2Schema, IdTypeSchema } from '@schemas/common/common';
import { MapboxIdSchema } from '@schemas/common/place';
import { CityMapboxSchema } from '@schemas/master/city';
import { LatitudeSchema, LongitudeSchema } from '@schemas/port/common';
import { isEmpty } from '@utils/data/object';
import { z } from 'zod';

export const AddressTextSchema = z.string().min(3).max(1000);
export type AddressText = z.infer<typeof AddressTextSchema>;

export const AddressRawDataMapboxSchema = z.object({
  id: MapboxIdSchema,
  text: AddressTextSchema,
});
export type CityMapboxI = z.infer<typeof CityMapboxSchema>;

export const AddressRawDataParamsSchema = z
  .object({
    text: AddressTextSchema,
    latitude: LatitudeSchema,
    longitude: LongitudeSchema,
    countryIso2: CountryIso2Schema,
    mapboxId: MapboxIdSchema,
    city: IdTypeSchema.optional(),
    cityMapbox: CityMapboxSchema.optional(),
  })
  .superRefine((data, _ctx) => {
    if (isEmpty(data?.city) && isEmpty(data?.cityMapbox)) {
      throw new AppError('ADDR003');
    }
  });

export type AddressRawDataParams = z.infer<typeof AddressRawDataParamsSchema>;
