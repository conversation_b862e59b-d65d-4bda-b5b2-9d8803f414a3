import { PlaceSourceE } from '@consts/master/place';
import { CountryIso2Schema, IdTypeSchema, PaginationSchema } from '@schemas/common/common';
import { MapboxIdSchema } from '@schemas/common/place';
import { LatitudeSchema, LongitudeSchema } from '@schemas/port/common';
import { z } from 'zod';

export const CityFetchOneParamsSchema = z.object({
  city: IdTypeSchema,
  countryIso2: CountryIso2Schema.optional(),
});
export type CityFetchOneParamsI = z.infer<typeof CityFetchOneParamsSchema>;

export const CityFetchForClientParamsSchema = z.object({
  search: z.string().min(1).optional(),
  countryIso2: CountryIso2Schema.optional(),
});
export type CityFetchForClientParamsI = z.infer<typeof CityFetchForClientParamsSchema>;

export const CityFetchForClientParamsPaginationSchema = CityFetchForClientParamsSchema.merge(PaginationSchema);
export const CityFetchsertParamsSchema = z.object({
  name: z.string().min(2).optional(),
  countryIso2: CountryIso2Schema.optional(),
  geoNameId: z.string().optional(),
  mapboxId: MapboxIdSchema.optional(),
});
export type CityFetchsertParamsI = z.infer<typeof CityFetchsertParamsSchema>;

export const CityNameSchema = z.string().min(2).max(100);
export type CityNameI = z.infer<typeof CityNameSchema>;

export const CityUpsertParamsSchema = z.object({
  name: CityNameSchema,
  countryIso2: CountryIso2Schema,
  latitude: LatitudeSchema,
  longitude: LongitudeSchema,
  geoNameId: z.string().optional(),
  source: PlaceSourceE,
});

export type CityUpsertParamsI = z.infer<typeof CityUpsertParamsSchema>;

export const CityMapboxSchema = z.object({
  id: MapboxIdSchema,
  name: CityNameSchema,
});

export type CityMapboxI = z.infer<typeof CityMapboxSchema>;
