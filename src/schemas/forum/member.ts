import { MemberTypeE } from '@consts/forum/member';
import { UUIDSchema } from '@schemas/common/common';
import z from 'zod';

export const CommunityMemberFetchForClientSchema = z.object({
  profileId: UUIDSchema,
  communityId: UUIDSchema,
});

export type CommunityMemberFetchForClientI = z.infer<typeof CommunityMemberFetchForClientSchema>;
export const CommunityMemberTypeChangeQuerySchema = z.object({
  memberId: UUIDSchema,
  communityId: UUIDSchema,
});

export const CommunityMemberTypeChangeSchema = z.object({
  communityId: UUIDSchema,
  profileId: UUIDSchema,
  requestedType: MemberTypeE,
});

export const CommunityMemberTypeChangeApprovalQuerySchema = z.object({
  communityId: UUIDSchema,
  profileId: UUIDSchema,
});

export const CommunityMemberTypeChangeApprovalBodySchema = z.object({
  acceptedType: MemberTypeE,
});
export const CommunityMemberUpsertSchema = z.object({
  communityId: UUIDSchema,
  profileId: UUIDSchema,
  type: MemberTypeE,
});

export type CommunityMemberTypeChangeI = z.infer<typeof CommunityMemberTypeChangeSchema>;
export type CommunityMemberTypeChangeApprovalQueryI = z.infer<typeof CommunityMemberTypeChangeApprovalQuerySchema>;
export type CommunityMemberTypeChangeApprovalBodyI = z.infer<typeof CommunityMemberTypeChangeApprovalBodySchema>;
export type CommunityMemberUpsertI = z.infer<typeof CommunityMemberUpsertSchema>;
