import { DocumentExpiryStatusI } from '@consts/document/common';

export const addMsToDate = ({ date = new Date(), ms }: { date?: Date; ms: number }) => new Date(date.getTime() + ms);
export const subMsToDate = ({ date = new Date(), ms }: { date?: Date; ms: number }) => new Date(date.getTime() - ms);

export const getExpiryStatus = ({
  fromDate,
  untilDate,
}: {
  fromDate: Date;
  untilDate: Date;
}): DocumentExpiryStatusI => {
  if (fromDate > untilDate) {
    return 'EXPIRED';
  } else if (untilDate.getMonth() - fromDate.getMonth() <= 6) {
    return 'EXPIRES_SOON';
  }
  return 'VALID';
};
export const getCurrentDate = (): Date => new Date();

export const isLessThanEqualDateRange = (startDate, endDate): boolean => startDate <= endDate;
