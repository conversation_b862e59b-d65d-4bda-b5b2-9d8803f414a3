import { customAlphabet } from 'nanoid';

const nanoid = customAlphabet('1234567890abcdef', 5);

export const generateSlug = (text: string, options: { maxLength?: number; keepCase?: boolean } = {}): string => {
  const { maxLength = 60, keepCase = false } = options;

  let slug = text
    .toString()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '');

  if (!keepCase) {
    slug = slug.toLowerCase();
  }

  if (slug.length > maxLength) {
    slug = slug.substring(0, maxLength).replace(/-+$/, '');
  }

  if (slug.length === 0) {
    return nanoid();
  }

  return `${slug}-${nanoid()}`;
};
