import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import ForumModule from '@modules/forum';
import { ForumExploreSchema } from '@schemas/forum/explore';
import { FastifyInstance, FastifyReply } from 'fastify';

const exploreRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/forum/explore', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: query, error } = ForumExploreSchema.safeParse(request.query);

    if (error) {
      throw new AppError('GEN002', error);
    }

    const result = await ForumModule.ExploreModule.fetchExploreData(request, query);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default exploreRoutes;
