import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import ForumModule from '@modules/forum';
import { RouteParamsSchema } from '@schemas/common/common';
import {
  AnswerUpdateStatusSchema,
  ForumAnswerCreateOneSchema,
  ForumAnswerFetchManySchema,
  SlugParamSchema,
} from '@schemas/forum/answer';
import { FastifyInstance, FastifyReply } from 'fastify';

const answerRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/forum/answers', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ForumAnswerFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('FMANS004', queryError);
    }
    const result = await ForumModule.AnswerModule.fetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/forum/answer/text', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: routeParamsError, data: routeParamsData } = RouteParamsSchema.safeParse(request.query);
    if (routeParamsError) {
      throw new AppError('FMANS006', routeParamsError);
    }
    const result = await ForumModule.AnswerModule.fetchOne(request, { id: routeParamsData.id! }, { text: true });
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/forum/answer/slug/:slug', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data } = SlugParamSchema.safeParse(request.params);
    if (error) {
      throw new AppError('FMANS006', error);
    }

    const result = await ForumModule.AnswerModule.fetchBySlug(data);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/forum/answer', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = ForumAnswerCreateOneSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('FMANS005', bodyError);
    }
    const result = await ForumModule.AnswerModule.createOne(request, bodyData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.delete('/backend/api/v1/forum/answer/:id', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: routeParamsError, data: routeParamsData } = RouteParamsSchema.safeParse(request.params);
    if (routeParamsError) {
      throw new AppError('FMANS006', routeParamsError);
    }
    await ForumModule.AnswerModule.deleteOne(request, routeParamsData);
    reply.status(HttpStatus.NO_CONTENT);
  });
  fastify.patch('/backend/api/v1/forum/answer-status', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = AnswerUpdateStatusSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('FMANS005', bodyError);
    }
    await ForumModule.AnswerModule.updateStatus(request, bodyData);
    return reply.code(HttpStatus.OK);
  });
};
export default answerRoutes;
