import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import FeedModule from '@modules/feed';
import { ReactionFetchManySchema, ReactionPostIdSchema, ReactionUpsertSchema } from '@schemas/feed/reaction';
import { FastifyInstance } from 'fastify';

import type { FastifyReply } from 'fastify';
const reactionRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/feed/reaction', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = ReactionUpsertSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('PRCT004', bodyError);
    }
    const result = await FeedModule.ReactionModule.upsert(request, bodyData);
    reply.status(HttpStatus.CREATED).send(result);
  });
  fastify.get('/backend/api/v1/feed/reactions', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = ReactionFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PRCT005', queryError);
    }
    const result = await FeedModule.ReactionModule.fetchMany(request, queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.delete('/backend/api/v1/feed/reaction', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = ReactionPostIdSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('PRCT006', queryError);
    }
    await FeedModule.ReactionModule.deleteOne(request, queryData);
    reply.status(HttpStatus.NO_CONTENT);
  });
};

export default reactionRoutes;
