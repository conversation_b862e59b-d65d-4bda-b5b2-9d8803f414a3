import { FastifyInstance, FastifyReply } from 'fastify';
import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';

import { AnnouncementCreateOneSchema } from '@schemas/announcement/announcement';
import { AnnouncementModule } from '@modules/announcement';

const coreAnnouncementRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/announcement', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error, data: body } = AnnouncementCreateOneSchema.safeParse(request.body);
    if (error) {
      throw new AppError('ANC006', error);
    }
    const result = await AnnouncementModule.CoreAnnouncementModule.createOne(body);
    reply.status(HttpStatus.CREATED).send(result);
  });
};

export default coreAnnouncementRoutes;
