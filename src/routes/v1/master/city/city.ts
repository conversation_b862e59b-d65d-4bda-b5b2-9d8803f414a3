import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import Master from '@modules/master';
import {
  CityFetchForClientParamsPaginationSchema,
  CityFetchsertParamsI,
  CityFetchsertParamsSchema,
} from '@schemas/master/city';

import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const cityRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/master/city/options', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error, data: query } = CityFetchForClientParamsPaginationSchema.safeParse(request.query);
    if (error) {
      throw new AppError('CITY005');
    }
    const result = await Master.CityModule.fetchForClient(
      pick(query, ['search', 'countryIso2']),
      pick(query, ['page', 'pageSize']),
    );
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/master/city/options', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error, data: body } = CityFetchsertParamsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('CITY006');
    }
    const result = await Master.CityModule.fetchsert(body as CityFetchsertParamsI, {
      OR: [
        {
          name: {
            startsWith: body.name.trim().toLowerCase(),
            mode: 'insensitive',
          },
        },
      ],
    });
    reply.status(HttpStatus.OK).send(result);
  });
};

export default cityRoutes;
