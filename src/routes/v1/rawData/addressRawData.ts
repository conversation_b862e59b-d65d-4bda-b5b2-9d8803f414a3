import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { AddressRawDataParamsSchema } from '@schemas/rawData/addressRawData';
import { AddressRawDataModule } from '@modules/rawData/addressRawData';
import { isFilled, pick } from '@utils/data/object';

const addressRawDataRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/rawData/address', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error, data: body } = AddressRawDataParamsSchema.safeParse(request.body);
    if (error) {
      throw new AppError('ADDR001');
    }
    const result = await AddressRawDataModule.fetchsert(
      {
        ...pick(body, ['countryIso2', 'text', 'latitude', 'longitude']),
        ...(isFilled(body?.city) ? { cityId: body.city.id } : {}),
      },
      { mapboxId: body.mapboxId },
    );
    reply.status(HttpStatus.OK).send(result);
  });
};

export default addressRawDataRoutes;
