import { FastifyInstance, FastifyReply } from 'fastify';
import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';

import { LeaderboardFetchParamsSchema } from '@schemas/leaderboard/leaderboard';
import { LeaderboardModule } from '@modules/leaderboard/leaderboard';

const coreLeaderboardRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/leaderboard', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = LeaderboardFetchParamsSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('LBD005', queryError);
    }

    const result = await LeaderboardModule.fetch(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default coreLeaderboardRoutes;
