import AppError from '@classes/AppError';
import { ScoreTypeE as ScoreType } from '@prisma/postgres';

export const ScoreTypeToFieldMap = {
  [ScoreType.CONTRIBUTION]: 'contributionScore',
  [ScoreType.QNA_ANSWER]: 'qnaAnswerScore',
  [ScoreType.TROUBLESHOOT_ANSWER]: 'troubleshootAnswerScore',
} as const;

export type ScoreFieldKey = 'contributionScore' | 'qnaAnswerScore' | 'troubleshootAnswerScore';

export const getScoreField = (type: ScoreType): ScoreFieldKey => {
  const field = ScoreTypeToFieldMap[type];
  if (!field) throw new AppError('RWD005');
  return field;
};
