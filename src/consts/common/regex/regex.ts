export const CargoNameR = /^[a-zA-Z0-9\s-'.()]+$/;
export const CertificateCourseNameR = /^[a-zA-Z0-9\s-'.()]+$/;
export const CountryISO2R = /^[A-Z]{2}$/;
export const CountryNameR = /^[a-zA-Z\s-'.()]+$/;
export const DegreeNameR = /^[a-zA-Z0-9\s-'.()]+$/;
export const DepartmentNameR = /^[a-zA-Z0-9\s-'.()]+$/;
export const DesignationNameR = /^[a-zA-Z0-9\s-'.()]+$/;
export const EntityNameR = /^[a-zA-Z0-9\s-'.(),]+$/;
export const EquipmentCategoryR = /^[a-zA-Z0-9\s-'.()]+$/;
export const EquipmentManufacturerR = /^[a-zA-Z0-9\s-'.()&]+$/;
export const EquipmentModelR = /^[a-zA-Z0-9\s-'.()]+$/;
export const FuelTypeR = /^[a-zA-Z0-9\s-'.()]+$/;
export const IsShipImoR = /^\d{1,7}$/;
export const MentionR = /@(\w+)/g;
export const ObjectIdR = /^[0-9a-fA-F]{24}$/;
export const PersonNameR = /^(?!\.')(?!.*\.\.)(?!.*\.$)[A-Za-z.]+(?:[ '.][A-Za-z.]+)*$/;
export const PortR = /^[a-zA-Z0-9\s-'.()]+$/;
export const SemverR =
  /^(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*))?(?:\+([a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*))?$/;
export const ShipR = /^[a-zA-Z0-9\s-'.()]+$/;
export const ShipCallSignR = /^[A-Z0-9]{4,7}$/;
export const ShipImoR = /^\d{7}$/;
export const ShipMmsiR = /^\d{9}$/;
export const ShipTypeR = /^[a-zA-Z0-9\s-'.()]+$/;
export const SkillNameR = /^[a-zA-Z0-9\s-'.()]+$/;
export const TextAlphaNumericSpecialCharR = /^[a-zA-Z0-9\s-'.()]+$/;
export const UnLocodeR = /^[A-Z]{5}$/;
export const VesselTypeR = /^[a-zA-Z0-9\s-'.()]+$/;
export const VisaNameR = /^[a-zA-Z0-9\s\-/'().]+$/;
export const YearR = /^\d{4}$/;
export const TopicR = /^[a-zA-Z0-9\s-'.()]+$/;
