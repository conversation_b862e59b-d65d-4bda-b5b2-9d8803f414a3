import { z } from 'zod';
import { ScoreTypeE } from '@prisma/postgres';

export const LeaderboardDurationE = z.enum(['WEEKLY', 'OVERALL']);
export type LeaderboardDuration = z.infer<typeof LeaderboardDurationE>;

export const LeaderboardTypeE = z.enum([
  ScoreTypeE.CONTRIBUTION,
  ScoreTypeE.QNA_ANSWER,
  ScoreTypeE.TROUBLESHOOT_ANSWER,
]);
export type LeaderboardType = z.infer<typeof LeaderboardTypeE>;
