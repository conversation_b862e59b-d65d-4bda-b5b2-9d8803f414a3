import type { Profile } from '@prisma/postgres';

export type LeaderboardStatI = {
  profileId: string;
  score: number;
  rank: number;
};

export type ProfileItemI = Pick<
  Profile,
  | 'id'
  | 'name'
  | 'avatar'
  | 'designationText'
  | 'entityText'
  | 'designationAlternativeId'
  | 'designationRawDataId'
  | 'entityId'
  | 'entityRawDataId'
>;

export type LeaderboardRawResultI = LeaderboardStatI & {
  Profile: ProfileItemI;
};

export type LeaderboardFetchForClientResultI = LeaderboardStatI & {
  Profile: {
    id: string;
    name: string | null;
    avatar: string | null;
    designation: {
      id: string;
      name: string;
      dataType: 'master' | 'raw';
    } | null;
    entity: {
      id: string;
      name: string;
      dataType: 'master' | 'raw';
    } | null;
  };
};
