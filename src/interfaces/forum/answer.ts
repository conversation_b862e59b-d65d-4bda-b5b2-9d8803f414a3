import type { AnswerStatusI, VoteTypeI } from '@consts/forum/answer';
import type { NullableI, StringNullI, TotalCursorDataI } from '@interfaces/common/data';
import type { ProfileExternalI, ProfileForDataI } from '@interfaces/user/profile';
import type { Answer, AnswerMedia } from '@prisma/postgres';
export type ForumAnswerMediaI = Pick<AnswerMedia, 'id' | 'fileExtension' | 'fileUrl'>;
export type ForumAnswerWithProfileSQLI = {
  id: string;
  cursorId: string;
  slug: string;
  text: string;
  isTextTruncated: boolean;
  upvoteCount: number;
  downvoteCount: number;
  vote: NullableI<VoteTypeI>;
  commentCount: number;
  status: AnswerStatusI;
  profileId: string;
  profileName: StringNullI;
  profileAvatar: StringNullI;
  profileDesignationText: StringNullI;
  profileDesignationAlternativeId: StringNullI;
  profileDesignationRawDataId: StringNullI;
  profileEntityText: StringNullI;
  profileEntityId: StringNullI;
  profileEntityRawDataId: StringNullI;
  media: NullableI<ForumAnswerMediaI[]>;
};
export type ForumAnswerWithProfileI = Pick<
  ForumAnswerWithProfileSQLI,
  'id' | 'text' | 'isTextTruncated' | 'upvoteCount' | 'downvoteCount' | 'commentCount' | 'status' | 'media' | 'slug'
> & {
  cursorId: number;
  vote: NullableI<VoteTypeI>;
  profile: ProfileExternalI;
  canModify: boolean;
  canUpdateStatus: boolean;
};
export type ForumAnswerFetchManyResultI = TotalCursorDataI<ForumAnswerWithProfileI>;
export type ForumAnswerWithProfileForQuestionI = Pick<
  Answer,
  'id' | 'text' | 'upvoteCount' | 'downvoteCount' | 'commentCount' | 'status' | 'isEdited' | 'createdAt'
> & { profile: ProfileForDataI; media: NullableI<ForumAnswerMediaI[]> };

export type ForumAnswerI = Pick<Answer, 'id'> & {
  cursorId: number;
};
