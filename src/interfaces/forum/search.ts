import { ForumQuestionDetailWithAnswersSQLI } from '@interfaces/forum/question';
import { FetchCommunityClientI } from '@interfaces/forum/community';

export type GlobalSearchQuestionItemI = Pick<
  ForumQuestionDetailWithAnswersSQLI,
  | 'id'
  | 'title'
  | 'description'
  | 'type'
  | 'answerCount'
  | 'upvoteCount'
  | 'createdAt'
  | 'profileName'
  | 'equipmentCategoryId'
  | 'equipmentCategoryName'
  | 'equipmentModelId'
  | 'equipmentModelName'
  | 'equipmentManufacturerId'
  | 'equipmentManufacturerName'
  | 'topics'
> & {
  communityId: string;
  communityName: string;
  departmentAlternativeId: string | null;
  departmentName: string | null;
  matchedFields: string[];
};

export type GlobalSearchCommunityItemI = Pick<
  FetchCommunityClientI,
  'id' | 'name' | 'access' | 'isRestricted' | 'memberCount' | 'questionCount'
> & {
  description: string | null;
  matchedFields: string[];
};

export type GlobalSearchResponseI<T = unknown> = {
  data: T[];
  total: number;
};

export type GlobalSearchCombinedResponseI = {
  questions?: GlobalSearchResponseI<GlobalSearchQuestionItemI>;
  communities?: GlobalSearchResponseI<GlobalSearchCommunityItemI>;
};
